import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Category from '@/models/Category';

export async function DELETE(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Category ID is required' }, { status: 400 });
    }
    
    const result = await Category.findByIdAndDelete(id);
    
    if (!result) {
      return NextResponse.json({ message: 'Category not found' }, { status: 404 });
    }
    
    return NextResponse.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    return NextResponse.json({ 
      message: 'Error deleting category', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function GET(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Category ID is required' }, { status: 400 });
    }
    
    const category = await Category.findById(id);
    
    if (!category) {
      return NextResponse.json({ message: 'Category not found' }, { status: 404 });
    }
    
    return NextResponse.json(category);
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json({ 
      message: 'Error fetching category', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function PUT(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    const body = await request.json();
    const { category_name } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'Category ID is required' }, { status: 400 });
    }
    
    if (!category_name) {
      return NextResponse.json({ error: 'Category name is required' }, { status: 400 });
    }
    
    // Check if another category with this name exists
    const existingCategory = await Category.findOne({ 
      category_name, 
      _id: { $ne: id } 
    });
    
    if (existingCategory) {
      return NextResponse.json({ 
        message: 'Category with this name already exists' 
      }, { status: 400 });
    }
    
    const updatedCategory = await Category.findByIdAndUpdate(
      id, 
      { category_name },
      { new: true, runValidators: true }
    );
    
    if (!updatedCategory) {
      return NextResponse.json({ message: 'Category not found' }, { status: 404 });
    }
    
    return NextResponse.json({ 
      message: 'Category updated successfully', 
      category: updatedCategory 
    });
  } catch (error) {
    console.error('Error updating category:', error);
    return NextResponse.json({ 
      message: 'Error updating category', 
      error: error.message 
    }, { status: 500 });
  }
}
