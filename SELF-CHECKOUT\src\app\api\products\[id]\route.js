import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Product from '@/models/Product';

// Helper function to generate barcode
function generateBarcode(productName, price) {
  const cleanName = productName.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  const priceStr = Math.floor(price || 0).toString().padStart(3, '0');
  return `${cleanName.slice(0, 4)}${timestamp}${priceStr}`;
}

export async function GET(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    const product = await Product.findById(id);
    
    if (!product) {
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }
    
    return NextResponse.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json({ 
      message: 'Error fetching product', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function PUT(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    const body = await request.json();
    const {
      productName,
      price,
      pricePerKg,
      quantity,
      productCode,
      description,
      category,
      supplier,
      selectedOption,
      barcode
    } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    if (!productName) {
      return NextResponse.json({ error: 'Product name is required' }, { status: 400 });
    }
    
    // Generate new barcode if not provided
    const productBarcode = barcode || generateBarcode(productName, price || pricePerKg);
    
    const updateData = {
      productName,
      barcode: productBarcode,
      price,
      pricePerKg,
      quantity,
      weight: body.weight || null,
      productCode,
      description,
      category,
      supplier,
      selectedOption,
    };
    
    const updatedProduct = await Product.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!updatedProduct) {
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }
    
    return NextResponse.json({ 
      message: 'Product updated successfully', 
      product: updatedProduct 
    });
  } catch (error) {
    console.error('Error updating product:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'Product with this barcode already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error updating product', 
      error: error.message 
    }, { status: 400 });
  }
}

export async function DELETE(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }
    
    const deletedProduct = await Product.findByIdAndDelete(id);
    
    if (!deletedProduct) {
      return NextResponse.json({ message: 'Product not found' }, { status: 404 });
    }
    
    return NextResponse.json({ 
      message: 'Product deleted successfully', 
      product: deletedProduct 
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json({ 
      message: 'Error deleting product', 
      error: error.message 
    }, { status: 500 });
  }
}
