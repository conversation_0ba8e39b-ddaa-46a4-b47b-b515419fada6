import React from 'react';

interface QrProps {
  totalPrice: number;
}

const Qr: React.FC<QrProps> = ({ totalPrice }) => {
  return (
    <div className="flex flex-col h-screen w-full p-4">
      {/* QR Code Section: Centered in the screen */}
      <div className="flex-grow flex flex-col justify-center items-center">
        <img src="/QR.svg" className="h-64 w-64 -mb-10" alt="QR Code" />
        <div className="mt-12 text-xl font-bold">Total Price: ₹{totalPrice.toFixed(2)}</div>
      </div>

      {/* AI-Powered Self-Checkout Text: Positioned at the bottom */}
      <div className="text-center text-gray-600 mt-40">
        <div className="text-sm font-medium">
          Experience the convenience of our <span className="font-bold text-blue-500">AI-powered self-checkout</span>—fast, secure, and hassle-free.
        </div>
      </div>
    </div>
  );
};

export default Qr;
