import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Category from '@/models/Category';

export async function GET(request) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const categoryName = searchParams.get('category_name');
    
    if (!categoryName) {
      return NextResponse.json({ error: 'category_name is required' }, { status: 400 });
    }
    
    const existingCategory = await Category.findOne({ category_name: categoryName });
    
    if (existingCategory) {
      return NextResponse.json({ exists: true });
    } else {
      return NextResponse.json({ exists: false });
    }
  } catch (error) {
    console.error('Error checking category name:', error);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
