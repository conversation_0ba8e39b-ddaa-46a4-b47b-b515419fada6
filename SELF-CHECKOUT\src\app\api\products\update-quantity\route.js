import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';

export async function POST(request) {
    try {
        await connectDB();
        
        const { cartItems } = await request.json();
        
        // Update quantities for all cart items
        for (let item of cartItems) {
            const { productName, quantity } = item;
            
            const product = await Product.findOne({
                productName: new RegExp(`^${productName}$`, 'i')
            });
            
            if (product) {
                product.quantity -= quantity;
                if (product.quantity < 0) {
                    product.quantity = 0;
                }
                await product.save();
            } else {
                return Response.json(
                    { success: false, message: `Product with name ${productName} not found` },
                    { status: 404 }
                );
            }
        }
        
        return Response.json({
            success: true,
            message: 'Product quantities updated successfully'
        });
    } catch (error) {
        console.error('Update quantity error:', error);
        return Response.json(
            { success: false, message: 'Error updating product quantities', error: error.message },
            { status: 500 }
        );
    }
}
