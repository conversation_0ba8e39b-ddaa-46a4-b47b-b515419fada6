// "use client";
// import React, { useRef, useState, useEffect } from "react";
// import "../../app/app.css";

// interface Point {
//   x: number;
//   y: number;
// }

// interface ROI {
//   x: number;
//   y: number;
//   width: number;
//   height: number;
// }

// const MotionDetection: React.FC = () => {
//   const canvasRef = useRef<HTMLCanvasElement | null>(null);
//   const overlayCanvasRef = useRef<HTMLCanvasElement | null>(null);
//   const imageRef = useRef<HTMLImageElement | null>(null);

//   const [showSettingsModal, setShowSettingsModal] = useState(false);
//   const [selectedPoints, setSelectedPoints] = useState<Point[]>([]);
//   const [roi, setRoi] = useState<ROI>({
//     x: 208,
//     y: 175,
//     width: 465 - 208,
//     height: 512 - 175,
//   });
//   const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
//   const [modalImageLoading, setModalImageLoading] = useState(false);

//   const [showMotionToast, setShowMotionToast] = useState(false);
//   const [showItemToast, setShowItemToast] = useState(false);

//   const motionDetectedRef = useRef(false);
//   const framesSinceMotionRef = useRef(0);
//   const cooldownRef = useRef(false);

//   // Notify endpoint
//   const notifyEndpoint = async (eventType: string) => {
//     try {
//       const response = await fetch("http://192.168.1.6:9000/predict", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ event: eventType }),
//       });

//       if (!response.ok) {
//         console.error(`Error notifying endpoint for ${eventType}`);
//       }
//     } catch (error) {
//       console.error(`Error calling endpoint for ${eventType}:`, error);
//     }
//   };

//   useEffect(() => {
//     if (showSettingsModal) {
//       setModalImageLoading(true);
//       fetch("http://192.168.1.50:9000/image")
//         .then((response) => response.blob())
//         .then((blob) => {
//           const url = URL.createObjectURL(blob);
//           setModalImageUrl(url);
//           setModalImageLoading(false);
//         })
//         .catch((error) => {
//           console.error("Error fetching image for modal:", error);
//           setModalImageLoading(false);
//         });
//     }
//   }, [showSettingsModal]);

//   useEffect(() => {
//     return () => {
//       if (modalImageUrl) {
//         URL.revokeObjectURL(modalImageUrl);
//       }
//     };
//   }, [modalImageUrl]);

//   const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
//     if (selectedPoints.length < 4 && imageRef.current) {
//       const rect = imageRef.current.getBoundingClientRect();
//       const x = event.clientX - rect.left;
//       const y = event.clientY - rect.top;
//       setSelectedPoints([...selectedPoints, { x, y }]);
//       drawMarker(x, y);
//     }
//   };

//   const drawMarker = (x: number, y: number) => {
//     if (overlayCanvasRef.current) {
//       const canvas = overlayCanvasRef.current;
//       const ctx = canvas.getContext("2d");
//       if (ctx) {
//         ctx.beginPath();
//         ctx.arc(x, y, 5, 0, 2 * Math.PI);
//         ctx.fillStyle = "red";
//         ctx.fill();
//       }
//     }
//   };

//   const resetSelection = () => {
//     setSelectedPoints([]);
//     clearCanvas();
//   };

//   const clearCanvas = () => {
//     if (overlayCanvasRef.current) {
//       const canvas = overlayCanvasRef.current;
//       const ctx = canvas.getContext("2d");
//       if (ctx) {
//         ctx.clearRect(0, 0, canvas.width, canvas.height);
//       }
//     }
//   };

//   const calculateRoi = (points: Point[]): ROI | null => {
//     if (points.length !== 4) {
//       return null;
//     }
//     const xs = points.map((point) => point.x);
//     const ys = points.map((point) => point.y);
//     const minX = Math.min(...xs);
//     const minY = Math.min(...ys);
//     const maxX = Math.max(...xs);
//     const maxY = Math.max(...ys);
//     return {
//       x: minX,
//       y: minY,
//       width: maxX - minX,
//       height: maxY - minY,
//     };
//   };

//   const saveRoi = () => {
//     const newRoi = calculateRoi(selectedPoints);
//     if (newRoi) {
//       setRoi(newRoi);
//       resetSelection();
//       setShowSettingsModal(false);
//     } else {
//       alert("Please select exactly four points.");
//     }
//   };

//   useEffect(() => {
//     if (imageRef.current && overlayCanvasRef.current) {
//       const canvas = overlayCanvasRef.current;
//       const ctx = canvas.getContext("2d");
//       if (ctx) {
//         canvas.width = imageRef.current.naturalWidth;
//         canvas.height = imageRef.current.naturalHeight;
//       }
//     }
//   }, [modalImageUrl]);

//   useEffect(() => {
//     const cellSize = 30;
//     const diffThreshold = 10000;
//     const processEveryNthFrame = 3;
//     let frameCounter = 0;
//     let previousGrayData: Uint8ClampedArray | null = null;

//     const fetchAndProcessImage = async () => {
//       try {
//         const response = await fetch("http://192.168.1.50:9000/image");
//         const blob = await response.blob();
//         const url = URL.createObjectURL(blob);
//         const image = new Image();
//         image.src = url;
//         image.onload = () => {
//           if (canvasRef.current) {
//             const canvas = canvasRef.current;
//             const ctx = canvas.getContext("2d");
//             if (!ctx) return;

//             canvas.width = image.width;
//             canvas.height = image.height;

//             ctx.clearRect(0, 0, canvas.width, canvas.height);
//             ctx.drawImage(image, 0, 0);

//             frameCounter++;
//             if (frameCounter % processEveryNthFrame !== 0) {
//               URL.revokeObjectURL(url);
//               return;
//             }

//             const { x: roiX, y: roiY, width, height } = roi;
//             const currentImageData = ctx.getImageData(roiX, roiY, width, height);
//             const currentGrayData = toGrayscale(currentImageData);

//             if (previousGrayData) {
//               const motionCells = detectMotion(
//                 previousGrayData,
//                 currentGrayData,
//                 width,
//                 height,
//                 cellSize,
//                 diffThreshold
//               );

//               ctx.globalCompositeOperation = "source-over";
//               ctx.fillStyle = "rgba(255, 0, 0, 0.5)";
//               motionCells.forEach((cell) => {
//                 ctx.fillRect(cell.x + roiX, cell.y + roiY, cellSize, cellSize);
//               });

//               const motionDetectedNow = motionCells.length > 0;
//               if (motionDetectedNow && !cooldownRef.current) {
//                 setShowMotionToast(true);
//                 notifyEndpoint("motion"); // Notify motion detected
//                 motionDetectedRef.current = true;
//                 framesSinceMotionRef.current = 0;
//                 cooldownRef.current = true;
//                 setTimeout(() => {
//                   setShowMotionToast(false);
//                   cooldownRef.current = false;
//                 }, 500);
//               } else if (motionDetectedRef.current) {
//                 framesSinceMotionRef.current += 1;
//                 if (framesSinceMotionRef.current >= 2) {
//                   setShowItemToast(true);
//                   notifyEndpoint("item"); // Notify item placed
//                   motionDetectedRef.current = false;
//                   framesSinceMotionRef.current = 0;
//                   setTimeout(() => {
//                     setShowItemToast(false);
//                   }, 1000);
//                 }
//               }

//               ctx.beginPath();
//               ctx.rect(roiX, roiY, width, height);
//               ctx.strokeStyle = "blue";
//               ctx.lineWidth = 2;
//               ctx.stroke();
//             }

//             previousGrayData = currentGrayData;
//             URL.revokeObjectURL(url);
//           }
//         };
//       } catch (error) {
//         console.error("Error fetching or processing image:", error);
//       }
//     };

//     const interval = setInterval(fetchAndProcessImage, 250);

//     return () => clearInterval(interval);
//   }, [roi]);

//   const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {
//     const { data, width, height } = imageData;
//     const grayData = new Uint8ClampedArray(width * height);
//     for (let i = 0; i < data.length; i += 4) {
//       const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
//       grayData[i / 4] = gray;
//     }
//     return grayData;
//   };

//   const detectMotion = (
//     prevGray: Uint8ClampedArray,
//     currentGray: Uint8ClampedArray,
//     width: number,
//     height: number,
//     cellSize: number,
//     threshold: number
//   ): Point[] => {
//     const motionCells: Point[] = [];
//     for (let y = 0; y <= height - cellSize; y += cellSize) {
//       for (let x = 0; x <= width - cellSize; x += cellSize) {
//         let cellDiff = 0;
//         for (let i = y; i < y + cellSize; i++) {
//           for (let j = x; j < x + cellSize; j++) {
//             const index = i * width + j;
//             if (index < prevGray.length) {
//               cellDiff += Math.abs(prevGray[index] - currentGray[index]);
//             }
//           }
//         }
//         if (cellDiff > threshold) {
//           motionCells.push({ x, y });
//         }
//       }
//     }
//     return motionCells;
//   };

//   return (
//     <div className="App">
//       <button onClick={() => setShowSettingsModal(true)}>
//         <i className="fas fa-cog"></i>
//       </button>
//       <canvas
//         ref={canvasRef}
//         width="640"
//         height="640"
//         style={{ marginTop: "130px" }}
//       ></canvas>
//       {showMotionToast && <div className="toast show">Motion Detected!</div>}
//       {showItemToast && <div className="toast show">Item placed</div>}
//       {showSettingsModal && (
//         <div className="modal">
//           {modalImageLoading ? (
//             <div>Loading image...</div>
//           ) : (
//             <div className="image-container">
//               <img
//                 ref={imageRef}
//                 src={modalImageUrl || ""}
//                 alt="Settings Image"
//                 onClick={handleImageClick}
//               />
//               <canvas
//                 ref={overlayCanvasRef}
//                 style={{ position: "absolute", top: 0, left: 0 }}
//               />
//             </div>
//           )}
//           <button onClick={resetSelection}>Reset</button>
//           <button onClick={saveRoi} disabled={selectedPoints.length < 4}>
//             Save
//           </button>
//           <button onClick={() => setShowSettingsModal(false)}>Close</button>
//         </div>
//       )}
//     </div>
//   );
// };

// export default MotionDetection;
"use client";
import React, { useRef, useState, useEffect } from "react";
import "../../app/app.css";

interface Point {
  x: number;
  y: number;
}

interface ROI {
  x: number;
  y: number;
  width: number;
  height: number;
}

const MotionDetection: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const imageRef = useRef<HTMLImageElement | null>(null);

  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedPoints, setSelectedPoints] = useState<Point[]>([]);
  const [roi, setRoi] = useState<ROI>({
    x: 208,
    y: 175,
    width: 465 - 208,
    height: 512 - 175,
  });
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [modalImageLoading, setModalImageLoading] = useState(false);

  const [showMotionToast, setShowMotionToast] = useState(false);
  const [showItemToast, setShowItemToast] = useState(false);

  const motionDetectedRef = useRef(false);
  const framesSinceMotionRef = useRef(0);
  const cooldownRef = useRef(false);

  // Notify endpoint with image
  const notifyEndpointWithImage = async (eventType: string) => {
    try {
      if (canvasRef.current) {
        const canvas = canvasRef.current;
        const blob = await new Promise<Blob | null>((resolve) =>
          canvas.toBlob(resolve, "image/png")
        );
        if (!blob) {
          console.error("Failed to create blob from canvas");
          return;
        }

        const formData = new FormData();
        formData.append("event", eventType);
        formData.append("image", blob, "capture.png");

        const response = await fetch("http://192.168.1.6:9000/predict", {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          console.error(`Error notifying endpoint for ${eventType}`);
        } else {
          console.log(`${eventType} notification sent successfully`);
        }
      }
    } catch (error) {
      console.error(`Error calling endpoint for ${eventType}:`, error);
    }
  };

  useEffect(() => {
    if (showSettingsModal) {
      setModalImageLoading(true);
      fetch("http://192.168.1.50:9000/image")
        .then((response) => response.blob())
        .then((blob) => {
          const url = URL.createObjectURL(blob);
          setModalImageUrl(url);
          setModalImageLoading(false);
        })
        .catch((error) => {
          console.error("Error fetching image for modal:", error);
          setModalImageLoading(false);
        });
    }
  }, [showSettingsModal]);

  useEffect(() => {
    return () => {
      if (modalImageUrl) {
        URL.revokeObjectURL(modalImageUrl);
      }
    };
  }, [modalImageUrl]);

  const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
    if (selectedPoints.length < 4 && imageRef.current) {
      const rect = imageRef.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      setSelectedPoints([...selectedPoints, { x, y }]);
      drawMarker(x, y);
    }
  };

  const drawMarker = (x: number, y: number) => {
    if (overlayCanvasRef.current) {
      const canvas = overlayCanvasRef.current;
      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.beginPath();
        ctx.arc(x, y, 5, 0, 2 * Math.PI);
        ctx.fillStyle = "red";
        ctx.fill();
      }
    }
  };

  const resetSelection = () => {
    setSelectedPoints([]);
    clearCanvas();
  };

  const clearCanvas = () => {
    if (overlayCanvasRef.current) {
      const canvas = overlayCanvasRef.current;
      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  };

  const calculateRoi = (points: Point[]): ROI | null => {
    if (points.length !== 4) {
      return null;
    }
    const xs = points.map((point) => point.x);
    const ys = points.map((point) => point.y);
    const minX = Math.min(...xs);
    const minY = Math.min(...ys);
    const maxX = Math.max(...xs);
    const maxY = Math.max(...ys);
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY,
    };
  };

  const saveRoi = () => {
    const newRoi = calculateRoi(selectedPoints);
    if (newRoi) {
      setRoi(newRoi);
      resetSelection();
      setShowSettingsModal(false);
    } else {
      alert("Please select exactly four points.");
    }
  };

  useEffect(() => {
    if (imageRef.current && overlayCanvasRef.current) {
      const canvas = overlayCanvasRef.current;
      const ctx = canvas.getContext("2d");
      if (ctx) {
        canvas.width = imageRef.current.naturalWidth;
        canvas.height = imageRef.current.naturalHeight;
      }
    }
  }, [modalImageUrl]);

  useEffect(() => {
    const cellSize = 30;
    const diffThreshold = 10000;
    const processEveryNthFrame = 3;
    let frameCounter = 0;
    let previousGrayData: Uint8ClampedArray | null = null;

    const fetchAndProcessImage = async () => {
      try {
        const response = await fetch("http://192.168.1.50:9000/image");
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const image = new Image();
        image.src = url;
        image.onload = () => {
          if (canvasRef.current) {
            const canvas = canvasRef.current;
            const ctx = canvas.getContext("2d");
            if (!ctx) return;

            canvas.width = image.width;
            canvas.height = image.height;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(image, 0, 0);

            frameCounter++;
            if (frameCounter % processEveryNthFrame !== 0) {
              URL.revokeObjectURL(url);
              return;
            }

            const { x: roiX, y: roiY, width, height } = roi;
            const currentImageData = ctx.getImageData(roiX, roiY, width, height);
            const currentGrayData = toGrayscale(currentImageData);

            if (previousGrayData) {
              const motionCells = detectMotion(
                previousGrayData,
                currentGrayData,
                width,
                height,
                cellSize,
                diffThreshold
              );

              ctx.globalCompositeOperation = "source-over";
              ctx.fillStyle = "rgba(255, 0, 0, 0.5)";
              motionCells.forEach((cell) => {
                ctx.fillRect(cell.x + roiX, cell.y + roiY, cellSize, cellSize);
              });

              const motionDetectedNow = motionCells.length > 0;
              if (motionDetectedNow && !cooldownRef.current) {
                setShowMotionToast(true);
                notifyEndpointWithImage("motion");
                motionDetectedRef.current = true;
                framesSinceMotionRef.current = 0;
                cooldownRef.current = true;
                setTimeout(() => {
                  setShowMotionToast(false);
                  cooldownRef.current = false;
                }, 500);
              } else if (motionDetectedRef.current) {
                framesSinceMotionRef.current += 1;
                if (framesSinceMotionRef.current >= 2) {
                  setShowItemToast(true);
                  notifyEndpointWithImage("item");
                  motionDetectedRef.current = false;
                  framesSinceMotionRef.current = 0;
                  setTimeout(() => {
                    setShowItemToast(false);
                  }, 1000);
                }
              }

              ctx.beginPath();
              ctx.rect(roiX, roiY, width, height);
              ctx.strokeStyle = "blue";
              ctx.lineWidth = 2;
              ctx.stroke();
            }

            previousGrayData = currentGrayData;
            URL.revokeObjectURL(url);
          }
        };
      } catch (error) {
        console.error("Error fetching or processing image:", error);
      }
    };

    const interval = setInterval(fetchAndProcessImage, 250);

    return () => clearInterval(interval);
  }, [roi]);

  const toGrayscale = (imageData: ImageData): Uint8ClampedArray => {
    const { data, width, height } = imageData;
    const grayData = new Uint8ClampedArray(width * height);
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
      grayData[i / 4] = gray;
    }
    return grayData;
  };

  const detectMotion = (
    prevGray: Uint8ClampedArray,
    currentGray: Uint8ClampedArray,
    width: number,
    height: number,
    cellSize: number,
    threshold: number
  ): Point[] => {
    const motionCells: Point[] = [];
    for (let y = 0; y <= height - cellSize; y += cellSize) {
      for (let x = 0; x <= width - cellSize; x += cellSize) {
        let cellDiff = 0;
        for (let i = y; i < y + cellSize; i++) {
          for (let j = x; j < x + cellSize; j++) {
            const index = i * width + j;
            if (index < prevGray.length) {
              cellDiff += Math.abs(prevGray[index] - currentGray[index]);
            }
          }
        }
        if (cellDiff > threshold) {
          motionCells.push({ x, y });
        }
      }
    }
    return motionCells;
  };

  return (
    <div className="App">
      <button onClick={() => setShowSettingsModal(true)}>
        <i className="fas fa-cog"></i>
      </button>
      <canvas
        ref={canvasRef}
        width="640"
        height="640"
        style={{ marginTop: "130px" }}
      ></canvas>
      {showMotionToast && <div className="toast show">Motion Detected!</div>}
      {showItemToast && <div className="toast show">Item placed</div>}
      {showSettingsModal && (
        <div className="modal">
          {modalImageLoading ? (
            <div>Loading image...</div>
          ) : (
            <div className="image-container">
              <img
                ref={imageRef}
                src={modalImageUrl || ""}
                alt="Settings Image"
                onClick={handleImageClick}
              />
              <canvas
                ref={overlayCanvasRef}
                style={{ position: "absolute", top: 0, left: 0 }}
              />
            </div>
          )}
          <button onClick={resetSelection}>Reset</button>
          <button onClick={saveRoi} disabled={selectedPoints.length < 4}>
            Save
          </button>
          <button onClick={() => setShowSettingsModal(false)}>Close</button>
        </div>
      )}
    </div>
  );
};

export default MotionDetection;
