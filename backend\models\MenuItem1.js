const mongoose = require('mongoose');

const menuItem1Schema = new mongoose.Schema({
  productName: {
    type: String,
    required: true,
  },
  barcode: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
  },
  shortCode: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  orderType: {
    type: String,
    required: true,
    enum: ['Delivery', 'Dine-In', 'Pickup'],
  },
});

module.exports = mongoose.model('MenuItem1', menuItem1Schema);
