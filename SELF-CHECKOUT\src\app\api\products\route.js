import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import dbConnect from '@/lib/mongodb';
import Product from '@/models/Product';

// Helper function to generate barcode
function generateBarcode(productName, price) {
  const cleanName = productName.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  const priceStr = Math.floor(price || 0).toString().padStart(3, '0');
  return `${cleanName.slice(0, 4)}${timestamp}${priceStr}`;
}

export async function GET(request) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    
    const skip = (page - 1) * limit;
    
    let query = {};
    if (category) {
      query.category = category;
    }
    
    const products = await Product.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    const total = await Product.countDocuments(query);
    
    return NextResponse.json({
      products,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalResults: total
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    await dbConnect();
    
    // Check if this is form data (file upload) or JSON
    const contentType = request.headers.get('content-type');
    let productData;
    let productImage = null;
    
    if (contentType && contentType.includes('multipart/form-data')) {
      // Handle file upload
      const formData = await request.formData();
      
      // Extract form fields
      productData = {
        productName: formData.get('productName'),
        price: parseFloat(formData.get('price')) || null,
        pricePerKg: parseFloat(formData.get('pricePerKg')) || null,
        quantity: parseInt(formData.get('quantity')) || 0,
        weight: parseFloat(formData.get('weight')) || null,
        productCode: formData.get('productCode'),
        description: formData.get('description'),
        category: formData.get('category'),
        supplier: formData.get('supplier'),
        selectedOption: formData.get('selectedOption'),
        barcode: formData.get('barcode')
      };
      
      // Handle file upload
      const file = formData.get('productImage');
      if (file && file.size > 0) {
        const sanitizedProductName = productData.productName.replace(/\s+/g, '_').toLowerCase();
        const fileExtension = file.name.split('.').pop();
        const filename = `${sanitizedProductName}.${fileExtension}`;
        
        // Save file to uploads directory
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const uploadDir = path.join(process.cwd(), 'public/uploads');
        
        // Create directory if it doesn't exist
        if (!existsSync(uploadDir)) {
          await mkdir(uploadDir, { recursive: true });
        }
        
        const filepath = path.join(uploadDir, filename);
        await writeFile(filepath, buffer);
        productImage = filename;
      }
    } else {
      // Handle JSON data
      productData = await request.json();
    }
    
    const {
      productName,
      price,
      pricePerKg,
      quantity,
      productCode,
      description,
      category,
      supplier,
      selectedOption,
      barcode
    } = productData;
    
    const productBarcode = barcode || generateBarcode(productName, price || pricePerKg);
    
    const newProduct = new Product({
      productName,
      barcode: productBarcode,
      price,
      pricePerKg,
      quantity,
      weight: productData.weight || null,
      productCode,
      description,
      category,
      supplier,
      productImage,
      selectedOption,
    });
    
    await newProduct.save();
    
    return NextResponse.json({ 
      message: 'Product added successfully', 
      newProduct 
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding product:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'Product with this barcode already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error adding product', 
      error: error.message 
    }, { status: 400 });
  }
}
