"use client";

import React, { useState } from "react";
import { IconButton } from "@mui/material";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";

interface WelcomeOverlayProps {
  onProceedToCart: () => void;
}

const WelcomeOverlay: React.FC<WelcomeOverlayProps> = ({ onProceedToCart }) => {
  const [isAnimating, setIsAnimating] = useState(false);

  const handleArrowClick = () => {
    setIsAnimating(true);
    // Wait for animation to complete before calling onProceedToCart
    setTimeout(() => {
      onProceedToCart();
    }, 800); // Animation duration
  };

  return (
    <div 
      className={`h-screen w-full relative overflow-hidden transition-transform duration-700 ease-in-out ${
        isAnimating ? 'transform -translate-y-full' : 'transform translate-y-0'
      }`}
      style={{
        backgroundImage: `url('/synecxai.png')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* <PERSON> at Bottom */}
      <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-20">
        <IconButton
          onClick={handleArrowClick}
          sx={{
            backgroundColor: "rgba(255, 255, 255, 0.9)",
            color: "#3B82F6",
            padding: "1.5rem",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 1)",
              transform: "scale(1.1)",
            },
            transition: "all 0.3s ease-in-out",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
            border: "2px solid rgba(59, 130, 246, 0.3)",
          }}
        >
          <ArrowUpwardIcon sx={{ fontSize: "2.5rem" }} />
        </IconButton>
      </div>
    </div>
  );
};

export default WelcomeOverlay;
