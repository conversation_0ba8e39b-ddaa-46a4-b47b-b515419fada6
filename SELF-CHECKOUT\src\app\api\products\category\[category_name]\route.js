import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';
import Category from '@/models/Category';

export async function DELETE(request, { params }) {
    try {
        await connectDB();
        
        const { category_name } = params;
        
        // Delete all products under this category
        const deletedProducts = await Product.deleteMany({ category: category_name });
        
        if (deletedProducts.deletedCount === 0) {
            return Response.json(
                { success: false, message: 'No products found under this category' },
                { status: 404 }
            );
        }
        
        // Delete the category itself
        const deletedCategory = await Category.findOneAndDelete({ category_name });
        
        if (!deletedCategory) {
            return Response.json(
                { success: false, message: 'Category not found' },
                { status: 404 }
            );
        }
        
        return Response.json({
            success: true,
            message: `Category "${category_name}" and its associated products have been deleted successfully.`,
            deletedProductsCount: deletedProducts.deletedCount
        });
    } catch (error) {
        console.error('Delete category error:', error);
        return Response.json(
            { success: false, message: 'Error deleting category and products', error: error.message },
            { status: 500 }
        );
    }
}
