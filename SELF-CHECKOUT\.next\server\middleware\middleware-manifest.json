{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_881373._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_fcd2e9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "giwyJ5dmojBysaDha3twhUpHopJM+Bl+MmLZLxX6VK4=", "__NEXT_PREVIEW_MODE_ID": "34c25d5c9e9c88498326fdad8a184650", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "756fbafe6b103c1b1f7545a61ff4d0e85899dc8a2d2003703dea93fdc932b58f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "45e8c5eb422c432dc44cfd9844b34f986bd104e75b385899f16e8e9d8f971178"}}}, "instrumentation": null, "functions": {}}