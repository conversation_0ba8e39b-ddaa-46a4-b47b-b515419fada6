import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';

export async function PUT(request) {
    try {
        await connectDB();
        
        const { productName, quantity } = await request.json();
        
        console.log('Received productName:', productName);
        console.log('Received quantity:', quantity);
        
        // Find product by name (case insensitive)
        const product = await Product.findOne({
            productName: new RegExp(`^${productName}$`, 'i')
        });
        
        if (product) {
            product.quantity += quantity;
            await product.save();
            
            return Response.json({
                success: true,
                message: 'Product quantity updated successfully',
                product
            });
        } else {
            return Response.json(
                { success: false, message: 'Product not found' },
                { status: 404 }
            );
        }
    } catch (error) {
        console.error('Replacement update error:', error);
        return Response.json(
            { success: false, message: 'Error updating product quantity', error: error.message },
            { status: 500 }
        );
    }
}
