const express = require('express');
const cors = require('cors');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();
const upload = multer({ dest: 'uploads/' });

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}

// Mock image endpoint - returns a simple test image
app.get('/image', (req, res) => {
  console.log('Image request received');
  
  // Create a simple 1x1 pixel PNG image
  const pngBuffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x02, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x90, 0x77, 0x53, 0xDE, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // image data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  res.set({
    'Content-Type': 'image/png',
    'Content-Length': pngBuffer.length
  });
  
  res.send(pngBuffer);
});

// Mock weight endpoint
app.get('/weight', (req, res) => {
  console.log('Weight request received');
  
  // Return a random weight between 0.1 and 2.0 kg
  const weight = Math.round((Math.random() * 1.9 + 0.1) * 1000) / 1000;
  
  res.json({
    weight: weight,
    unit: 'kg',
    timestamp: new Date().toISOString()
  });
});

// Mock prediction endpoint
app.post('/predict', upload.single('image'), (req, res) => {
  console.log('Prediction request received');
  console.log('File:', req.file ? req.file.filename : 'No file');
  
  // Mock prediction response
  const mockProducts = [
    'KAJU-KATLI',
    'MYSORE-PAK', 
    'GULAB-JAMUN',
    'LADDU',
    'MILK-PEDA',
    'BOMBAY-HALWA'
  ];
  
  // Randomly select 1-3 products
  const numProducts = Math.floor(Math.random() * 3) + 1;
  const selectedProducts = [];
  
  for (let i = 0; i < numProducts; i++) {
    const randomProduct = mockProducts[Math.floor(Math.random() * mockProducts.length)];
    if (!selectedProducts.includes(randomProduct)) {
      selectedProducts.push(randomProduct);
    }
  }
  
  // Create mock clip_tag (alternatives for each product)
  const clip_tag = selectedProducts.map(product => {
    const alternatives = mockProducts.filter(p => p !== product).slice(0, 2);
    return [product, ...alternatives];
  });
  
  const response = {
    yolo_tag: selectedProducts,
    clip_tag: clip_tag,
    weight_flag: Math.random() > 0.5 ? 1 : 0, // Randomly weight-based or count-based
    confidence: Math.round((Math.random() * 0.3 + 0.7) * 100) / 100, // 0.7-1.0
    timestamp: new Date().toISOString()
  };
  
  console.log('Sending prediction response:', response);
  
  // Clean up uploaded file
  if (req.file) {
    fs.unlink(req.file.path, (err) => {
      if (err) console.error('Error deleting file:', err);
    });
  }
  
  res.json(response);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    endpoints: {
      image: '/image',
      weight: '/weight',
      predict: '/predict (POST)'
    }
  });
});

const PORT = process.env.PORT || 9000;

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`  GET  http://localhost:${PORT}/image`);
  console.log(`  GET  http://localhost:${PORT}/weight`);
  console.log(`  POST http://localhost:${PORT}/predict`);
  console.log(`  GET  http://localhost:${PORT}/health`);
});
