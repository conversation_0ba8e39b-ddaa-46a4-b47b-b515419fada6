import { connectDB } from '@/lib/mongodb';
import User from '@/models/User';

export async function POST(request) {
    try {
        await connectDB();
        
        const { username, email, storename, role } = await request.json();
        
        // Note: In production, you should get the user ID from the JWT token
        // For now, we'll use a placeholder approach
        const userId = 'user-id'; // This should come from JWT token validation
        
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            { username, email, storename, role },
            { new: true, runValidators: true }
        );
        
        if (!updatedUser) {
            return Response.json(
                { success: false, message: 'User not found' },
                { status: 404 }
            );
        }
        
        return Response.json({
            success: true,
            message: 'Profile updated successfully!'
        });
    } catch (error) {
        console.error('Profile update error:', error);
        return Response.json(
            { success: false, message: 'Error: ' + error.message },
            { status: 400 }
        );
    }
}
