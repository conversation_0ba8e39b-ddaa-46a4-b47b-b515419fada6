import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';

export async function GET(request, { params }) {
    try {
        await connectDB();
        
        const { barcode } = params;
        
        console.log('Looking up barcode:', barcode);
        
        const product = await Product.findOne({ barcode });
        console.log('Found product:', product);
        
        if (!product) {
            console.log('No product found for barcode:', barcode);
            return Response.json(
                { success: false, message: 'Product not found' },
                { status: 404 }
            );
        }
        
        return Response.json({
            success: true,
            data: product
        });
    } catch (error) {
        console.error('Barcode lookup error:', error);
        return Response.json(
            { success: false, message: 'Server error', error: error.message },
            { status: 500 }
        );
    }
}
