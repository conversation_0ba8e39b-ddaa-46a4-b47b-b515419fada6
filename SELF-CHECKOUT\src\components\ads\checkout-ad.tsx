"use client";
import React, { useEffect, useState } from "react";
import FinalPayment from "../payment/final-payment"; // Adjust the import path as needed

interface CheckoutAdProps {
  totalPrice: number;
}

const CheckoutAd: React.FC<CheckoutAdProps> = ({ totalPrice }) => {


  const gifs = [
    { src: "/diary milk.gif", name: "Dairy Milk" },
    { src: "/icecreamV.jpg", name: "Vanilla Ice Cream" },
    { src: "/cake.jpg", name: "Choco Truffle Cake" },
    { src: "/brownie.jpg", name: "Chocolate Brownie" },
    { src: "/rama.jpg", name: "Rasamalai" },
  ];

  const [currentGifIndex, setCurrentGifIndex] = useState(0);
  const [showFinalPayment, setShowFinalPayment] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentGifIndex((prevIndex) => (prevIndex + 1) % gifs.length);
    }, 5000); // Change GIF every 5 seconds

    return () => clearInterval(interval);
  }, [gifs.length]);

  // const handleAddProduct = () => {
  //   const currentProduct = gifs[currentGifIndex].name;
  //   alert(`Added ${currentProduct} to the cart!`);
  // };

  const handleContinue = () => {
    setShowFinalPayment(true); // Show FinalPayment component
  };



  if (showFinalPayment) {
    return <FinalPayment totalPrice={totalPrice} />; // Render FinalPayment
  }

  return (
    <div className="h-[700px] w-full bg-white rounded-xl flex flex-col justify-between items-center p-4">
      {/* Display the GIF */}
      <div className="flex-grow w-full flex justify-center items-center bg-white rounded-lg">
        <img
          src={gifs[currentGifIndex].src}
          className="h-[660px] object-contain rounded-xl"
        />
      </div>

      {/* Buttons */}
      <div className="mt-4 flex justify-center gap-4">
      
        <button
          onClick={handleContinue}
          className="bg-blue-500 text-white px-6 py-3 rounded-lg text-lg font-bold hover:bg-gray-600"
        >
        PROCEED
        </button>
        
      </div>
    </div>
  );
};

export default CheckoutAd;
