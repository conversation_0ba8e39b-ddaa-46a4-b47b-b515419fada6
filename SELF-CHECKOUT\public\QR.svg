<?xml version="1.0" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1014" viewBox="0 0 1000 990"><defs><clipPath id="clip-path-dot-color"><path d="M 250 343v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,260,353)"/><path d="M 250 423v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,260,433)"/><path d="M 250 443v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,260,453)"/><circle cx="260" cy="493" r="10" transform="rotate(0,260,493)"/><path d="M 270 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,280,333)"/><rect x="270" y="343" width="20" height="20" transform="rotate(0,280,353)"/><path d="M 270 383v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,280,393)"/><rect x="270" y="403" width="20" height="20" transform="rotate(0,280,413)"/><path d="M 270 423v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,280,433)"/><circle cx="280" cy="473" r="10" transform="rotate(0,280,473)"/><rect x="290" y="343" width="20" height="20" transform="rotate(0,300,353)"/><rect x="290" y="363" width="20" height="20" transform="rotate(0,300,373)"/><rect x="290" y="383" width="20" height="20" transform="rotate(0,300,393)"/><circle cx="300" cy="493" r="10" transform="rotate(0,300,493)"/><path d="M 310 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,320,333)"/><path d="M 310 343v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,320,353)"/><path d="M 310 383v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,320,393)"/><path d="M 310 423v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,320,433)"/><rect x="310" y="443" width="20" height="20" transform="rotate(0,320,453)"/><path d="M 310 463v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,320,473)"/><path d="M 330 363v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,340,373)"/><path d="M 330 403v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,340,413)"/><path d="M 330 423v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,340,433)"/><path d="M 330 483v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,340,493)"/><path d="M 350 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,360,333)"/><rect x="350" y="363" width="20" height="20" transform="rotate(0,360,373)"/><path d="M 350 443v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,360,453)"/><rect x="350" y="463" width="20" height="20" transform="rotate(0,360,473)"/><rect x="350" y="483" width="20" height="20" transform="rotate(0,360,493)"/><path d="M 370 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,380,333)"/><path d="M 370 363v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,380,373)"/><path d="M 370 403v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,380,413)"/><path d="M 370 443v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,380,453)"/><rect x="370" y="483" width="20" height="20" transform="rotate(0,380,493)"/><rect x="390" y="403" width="20" height="20" transform="rotate(0,400,413)"/><path d="M 390 483v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,400,493)"/><path d="M 410 163v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,420,173)"/><rect x="410" y="183" width="20" height="20" transform="rotate(0,420,193)"/><rect x="410" y="203" width="20" height="20" transform="rotate(0,420,213)"/><rect x="410" y="223" width="20" height="20" transform="rotate(0,420,233)"/><path d="M 410 243v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,420,253)"/><path d="M 410 283v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,420,293)"/><rect x="410" y="303" width="20" height="20" transform="rotate(0,420,313)"/><rect x="410" y="323" width="20" height="20" transform="rotate(0,420,333)"/><rect x="410" y="343" width="20" height="20" transform="rotate(0,420,353)"/><path d="M 410 363v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,420,373)"/><path d="M 410 403v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,420,413)"/><circle cx="420" cy="453" r="10" transform="rotate(0,420,453)"/><path d="M 410 503v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,420,513)"/><path d="M 410 543v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,420,553)"/><circle cx="420" cy="593" r="10" transform="rotate(0,420,593)"/><path d="M 410 623v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,420,633)"/><path d="M 430 183v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,440,193)"/><rect x="430" y="203" width="20" height="20" transform="rotate(0,440,213)"/><rect x="430" y="223" width="20" height="20" transform="rotate(0,440,233)"/><rect x="430" y="243" width="20" height="20" transform="rotate(0,440,253)"/><path d="M 430 263v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,440,273)"/><rect x="430" y="303" width="20" height="20" transform="rotate(0,440,313)"/><rect x="430" y="343" width="20" height="20" transform="rotate(0,440,353)"/><path d="M 430 503v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,440,513)"/><rect x="430" y="543" width="20" height="20" transform="rotate(0,440,553)"/><rect x="430" y="623" width="20" height="20" transform="rotate(0,440,633)"/><path d="M 450 283v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,460,293)"/><rect x="450" y="303" width="20" height="20" transform="rotate(0,460,313)"/><rect x="450" y="323" width="20" height="20" transform="rotate(0,460,333)"/><rect x="450" y="343" width="20" height="20" transform="rotate(0,460,353)"/><path d="M 450 383v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,460,393)"/><rect x="450" y="403" width="20" height="20" transform="rotate(0,460,413)"/><rect x="450" y="423" width="20" height="20" transform="rotate(0,460,433)"/><rect x="450" y="443" width="20" height="20" transform="rotate(0,460,453)"/><rect x="450" y="463" width="20" height="20" transform="rotate(0,460,473)"/><path d="M 450 483v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,460,493)"/><path d="M 450 523v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,460,533)"/><path d="M 450 543v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,460,553)"/><circle cx="460" cy="593" r="10" transform="rotate(0,460,593)"/><path d="M 450 623v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,460,633)"/><path d="M 450 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,460,653)"/><path d="M 470 163v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,480,173)"/><rect x="470" y="183" width="20" height="20" transform="rotate(0,480,193)"/><rect x="470" y="203" width="20" height="20" transform="rotate(0,480,213)"/><rect x="470" y="223" width="20" height="20" transform="rotate(0,480,233)"/><path d="M 470 243v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,480,253)"/><rect x="470" y="343" width="20" height="20" transform="rotate(0,480,353)"/><rect x="470" y="363" width="20" height="20" transform="rotate(0,480,373)"/><rect x="470" y="383" width="20" height="20" transform="rotate(0,480,393)"/><path d="M 470 483v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,480,493)"/><rect x="470" y="643" width="20" height="20" transform="rotate(0,480,653)"/><rect x="490" y="163" width="20" height="20" transform="rotate(0,500,173)"/><path d="M 490 223v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,500,233)"/><rect x="490" y="243" width="20" height="20" transform="rotate(0,500,253)"/><rect x="490" y="263" width="20" height="20" transform="rotate(0,500,273)"/><path d="M 490 283v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,500,293)"/><path d="M 490 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,500,333)"/><path d="M 490 343v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,500,353)"/><path d="M 490 383v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,500,393)"/><rect x="490" y="403" width="20" height="20" transform="rotate(0,500,413)"/><rect x="490" y="423" width="20" height="20" transform="rotate(0,500,433)"/><path d="M 490 443v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,500,453)"/><path d="M 490 503v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,500,513)"/><rect x="490" y="523" width="20" height="20" transform="rotate(0,500,533)"/><rect x="490" y="543" width="20" height="20" transform="rotate(0,500,553)"/><path d="M 490 563v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,500,573)"/><path d="M 490 603v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,500,613)"/><rect x="490" y="623" width="20" height="20" transform="rotate(0,500,633)"/><path d="M 490 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,500,653)"/><rect x="510" y="163" width="20" height="20" transform="rotate(0,520,173)"/><path d="M 510 263v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,520,273)"/><circle cx="520" cy="373" r="10" transform="rotate(0,520,373)"/><path d="M 510 403v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,520,413)"/><rect x="510" y="423" width="20" height="20" transform="rotate(0,520,433)"/><path d="M 510 483v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,520,493)"/><rect x="510" y="543" width="20" height="20" transform="rotate(0,520,553)"/><path d="M 510 583v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,520,593)"/><rect x="510" y="623" width="20" height="20" transform="rotate(0,520,633)"/><rect x="530" y="163" width="20" height="20" transform="rotate(0,540,173)"/><circle cx="540" cy="233" r="10" transform="rotate(0,540,233)"/><circle cx="540" cy="293" r="10" transform="rotate(0,540,293)"/><circle cx="540" cy="333" r="10" transform="rotate(0,540,333)"/><path d="M 530 423v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,540,433)"/><path d="M 530 463v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,540,473)"/><rect x="530" y="483" width="20" height="20" transform="rotate(0,540,493)"/><rect x="530" y="503" width="20" height="20" transform="rotate(0,540,513)"/><rect x="530" y="523" width="20" height="20" transform="rotate(0,540,533)"/><rect x="530" y="543" width="20" height="20" transform="rotate(0,540,553)"/><rect x="530" y="563" width="20" height="20" transform="rotate(0,540,573)"/><path d="M 530 583v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,540,593)"/><path d="M 530 623v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,540,633)"/><path d="M 530 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,540,653)"/><rect x="550" y="163" width="20" height="20" transform="rotate(0,560,173)"/><circle cx="560" cy="213" r="10" transform="rotate(0,560,213)"/><path d="M 550 243v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,560,253)"/><path d="M 550 263v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,560,273)"/><path d="M 550 303v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,560,313)"/><path d="M 550 403v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,560,413)"/><path d="M 550 463v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,560,473)"/><rect x="550" y="543" width="20" height="20" transform="rotate(0,560,553)"/><rect x="550" y="563" width="20" height="20" transform="rotate(0,560,573)"/><path d="M 550 643v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,560,653)"/><path d="M 570 163v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,580,173)"/><path d="M 570 263v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,580,273)"/><rect x="570" y="283" width="20" height="20" transform="rotate(0,580,293)"/><path d="M 570 303v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,580,313)"/><path d="M 570 343v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,580,353)"/><rect x="570" y="363" width="20" height="20" transform="rotate(0,580,373)"/><rect x="570" y="383" width="20" height="20" transform="rotate(0,580,393)"/><rect x="570" y="403" width="20" height="20" transform="rotate(0,580,413)"/><path d="M 570 423v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,580,433)"/><path d="M 570 483v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,580,493)"/><rect x="570" y="503" width="20" height="20" transform="rotate(0,580,513)"/><rect x="570" y="523" width="20" height="20" transform="rotate(0,580,533)"/><rect x="570" y="543" width="20" height="20" transform="rotate(0,580,553)"/><rect x="570" y="563" width="20" height="20" transform="rotate(0,580,573)"/><circle cx="580" cy="613" r="10" transform="rotate(0,580,613)"/><path d="M 590 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,600,333)"/><path d="M 590 363v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,600,373)"/><path d="M 590 443v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,600,453)"/><rect x="590" y="463" width="20" height="20" transform="rotate(0,600,473)"/><rect x="590" y="483" width="20" height="20" transform="rotate(0,600,493)"/><rect x="590" y="563" width="20" height="20" transform="rotate(0,600,573)"/><path d="M 590 583v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,600,593)"/><path d="M 610 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(0,620,333)"/><circle cx="620" cy="413" r="10" transform="rotate(0,620,413)"/><rect x="610" y="483" width="20" height="20" transform="rotate(0,620,493)"/><circle cx="620" cy="533" r="10" transform="rotate(0,620,533)"/><rect x="610" y="563" width="20" height="20" transform="rotate(0,620,573)"/><path d="M 610 603v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,620,613)"/><rect x="610" y="623" width="20" height="20" transform="rotate(0,620,633)"/><path d="M 610 643v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,620,653)"/><path d="M 630 363v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,640,373)"/><path d="M 630 443v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,640,453)"/><rect x="630" y="483" width="20" height="20" transform="rotate(0,640,493)"/><rect x="630" y="563" width="20" height="20" transform="rotate(0,640,573)"/><path d="M 630 583v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,640,593)"/><path d="M 650 323v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,660,333)"/><rect x="650" y="363" width="20" height="20" transform="rotate(0,660,373)"/><path d="M 650 403v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,660,413)"/><rect x="650" y="423" width="20" height="20" transform="rotate(0,660,433)"/><rect x="650" y="443" width="20" height="20" transform="rotate(0,660,453)"/><rect x="650" y="463" width="20" height="20" transform="rotate(0,660,473)"/><rect x="650" y="483" width="20" height="20" transform="rotate(0,660,493)"/><rect x="650" y="503" width="20" height="20" transform="rotate(0,660,513)"/><rect x="650" y="523" width="20" height="20" transform="rotate(0,660,533)"/><rect x="650" y="543" width="20" height="20" transform="rotate(0,660,553)"/><rect x="650" y="563" width="20" height="20" transform="rotate(0,660,573)"/><path d="M 650 603v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(180,660,613)"/><rect x="670" y="323" width="20" height="20" transform="rotate(0,680,333)"/><rect x="670" y="363" width="20" height="20" transform="rotate(0,680,373)"/><path d="M 670 383v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(90,680,393)"/><rect x="670" y="443" width="20" height="20" transform="rotate(0,680,453)"/><rect x="670" y="463" width="20" height="20" transform="rotate(0,680,473)"/><rect x="670" y="483" width="20" height="20" transform="rotate(0,680,493)"/><rect x="670" y="503" width="20" height="20" transform="rotate(0,680,513)"/><path d="M 670 523v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,680,533)"/><path d="M 670 563v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,680,573)"/><rect x="670" y="583" width="20" height="20" transform="rotate(0,680,593)"/><rect x="670" y="603" width="20" height="20" transform="rotate(0,680,613)"/><rect x="670" y="623" width="20" height="20" transform="rotate(0,680,633)"/><path d="M 670 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,680,653)"/><rect x="690" y="323" width="20" height="20" transform="rotate(0,700,333)"/><rect x="690" y="343" width="20" height="20" transform="rotate(0,700,353)"/><rect x="690" y="363" width="20" height="20" transform="rotate(0,700,373)"/><path d="M 690 423v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,700,433)"/><rect x="690" y="443" width="20" height="20" transform="rotate(0,700,453)"/><rect x="690" y="483" width="20" height="20" transform="rotate(0,700,493)"/><path d="M 690 503v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,700,513)"/><rect x="690" y="583" width="20" height="20" transform="rotate(0,700,593)"/><path d="M 690 603v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,700,613)"/><rect x="690" y="643" width="20" height="20" transform="rotate(0,700,653)"/><rect x="710" y="323" width="20" height="20" transform="rotate(0,720,333)"/><rect x="710" y="343" width="20" height="20" transform="rotate(0,720,353)"/><path d="M 710 363v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,720,373)"/><rect x="710" y="443" width="20" height="20" transform="rotate(0,720,453)"/><rect x="710" y="463" width="20" height="20" transform="rotate(0,720,473)"/><path d="M 710 483v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,720,493)"/><path d="M 710 543v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,720,553)"/><rect x="710" y="563" width="20" height="20" transform="rotate(0,720,573)"/><rect x="710" y="583" width="20" height="20" transform="rotate(0,720,593)"/><rect x="710" y="643" width="20" height="20" transform="rotate(0,720,653)"/><path d="M 730 323v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,740,333)"/><path d="M 730 343v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,740,353)"/><path d="M 730 383v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,740,393)"/><rect x="730" y="403" width="20" height="20" transform="rotate(0,740,413)"/><rect x="730" y="423" width="20" height="20" transform="rotate(0,740,433)"/><rect x="730" y="443" width="20" height="20" transform="rotate(0,740,453)"/><path d="M 730 463v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,740,473)"/><path d="M 730 503v 20h 10a 10 10, 0, 0, 0, 0 -20" transform="rotate(-90,740,513)"/><rect x="730" y="523" width="20" height="20" transform="rotate(0,740,533)"/><rect x="730" y="543" width="20" height="20" transform="rotate(0,740,553)"/><rect x="730" y="563" width="20" height="20" transform="rotate(0,740,573)"/><rect x="730" y="583" width="20" height="20" transform="rotate(0,740,593)"/><rect x="730" y="603" width="20" height="20" transform="rotate(0,740,613)"/><rect x="730" y="623" width="20" height="20" transform="rotate(0,740,633)"/><path d="M 730 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,740,653)"/></clipPath><clipPath id="clip-path-corners-square-color-0-0"><path d="M 250 163v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,260,173)"/><rect x="250" y="183" width="20" height="20" transform="rotate(0,260,193)"/><rect x="250" y="203" width="20" height="20" transform="rotate(0,260,213)"/><rect x="250" y="223" width="20" height="20" transform="rotate(0,260,233)"/><rect x="250" y="243" width="20" height="20" transform="rotate(0,260,253)"/><rect x="250" y="263" width="20" height="20" transform="rotate(0,260,273)"/><path d="M 250 283v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,260,293)"/><rect x="270" y="163" width="20" height="20" transform="rotate(0,280,173)"/><rect x="270" y="283" width="20" height="20" transform="rotate(0,280,293)"/><rect x="290" y="163" width="20" height="20" transform="rotate(0,300,173)"/><rect x="290" y="283" width="20" height="20" transform="rotate(0,300,293)"/><rect x="310" y="163" width="20" height="20" transform="rotate(0,320,173)"/><rect x="310" y="283" width="20" height="20" transform="rotate(0,320,293)"/><rect x="330" y="163" width="20" height="20" transform="rotate(0,340,173)"/><rect x="330" y="283" width="20" height="20" transform="rotate(0,340,293)"/><rect x="350" y="163" width="20" height="20" transform="rotate(0,360,173)"/><rect x="350" y="283" width="20" height="20" transform="rotate(0,360,293)"/><path d="M 370 163v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,380,173)"/><rect x="370" y="183" width="20" height="20" transform="rotate(0,380,193)"/><rect x="370" y="203" width="20" height="20" transform="rotate(0,380,213)"/><rect x="370" y="223" width="20" height="20" transform="rotate(0,380,233)"/><rect x="370" y="243" width="20" height="20" transform="rotate(0,380,253)"/><rect x="370" y="263" width="20" height="20" transform="rotate(0,380,273)"/><path d="M 370 283v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,380,293)"/></clipPath><clipPath id="clip-path-corners-dot-color-0-0"><rect x="290" y="203" width="60" height="60" transform="rotate(0,320,233)"/></clipPath><clipPath id="clip-path-corners-square-color-1-0"><path d="M 610 163v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,620,173)"/><rect x="610" y="183" width="20" height="20" transform="rotate(0,620,193)"/><rect x="610" y="203" width="20" height="20" transform="rotate(0,620,213)"/><rect x="610" y="223" width="20" height="20" transform="rotate(0,620,233)"/><rect x="610" y="243" width="20" height="20" transform="rotate(0,620,253)"/><rect x="610" y="263" width="20" height="20" transform="rotate(0,620,273)"/><path d="M 610 283v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,620,293)"/><rect x="630" y="163" width="20" height="20" transform="rotate(0,640,173)"/><rect x="630" y="283" width="20" height="20" transform="rotate(0,640,293)"/><rect x="650" y="163" width="20" height="20" transform="rotate(0,660,173)"/><rect x="650" y="283" width="20" height="20" transform="rotate(0,660,293)"/><rect x="670" y="163" width="20" height="20" transform="rotate(0,680,173)"/><rect x="670" y="283" width="20" height="20" transform="rotate(0,680,293)"/><rect x="690" y="163" width="20" height="20" transform="rotate(0,700,173)"/><rect x="690" y="283" width="20" height="20" transform="rotate(0,700,293)"/><rect x="710" y="163" width="20" height="20" transform="rotate(0,720,173)"/><rect x="710" y="283" width="20" height="20" transform="rotate(0,720,293)"/><path d="M 730 163v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,740,173)"/><rect x="730" y="183" width="20" height="20" transform="rotate(0,740,193)"/><rect x="730" y="203" width="20" height="20" transform="rotate(0,740,213)"/><rect x="730" y="223" width="20" height="20" transform="rotate(0,740,233)"/><rect x="730" y="243" width="20" height="20" transform="rotate(0,740,253)"/><rect x="730" y="263" width="20" height="20" transform="rotate(0,740,273)"/><path d="M 730 283v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,740,293)"/></clipPath><clipPath id="clip-path-corners-dot-color-1-0"><rect x="650" y="203" width="60" height="60" transform="rotate(90,680,233)"/></clipPath><clipPath id="clip-path-corners-square-color-0-1"><path d="M 250 523v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(-90,260,533)"/><rect x="250" y="543" width="20" height="20" transform="rotate(0,260,553)"/><rect x="250" y="563" width="20" height="20" transform="rotate(0,260,573)"/><rect x="250" y="583" width="20" height="20" transform="rotate(0,260,593)"/><rect x="250" y="603" width="20" height="20" transform="rotate(0,260,613)"/><rect x="250" y="623" width="20" height="20" transform="rotate(0,260,633)"/><path d="M 250 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(180,260,653)"/><rect x="270" y="523" width="20" height="20" transform="rotate(0,280,533)"/><rect x="270" y="643" width="20" height="20" transform="rotate(0,280,653)"/><rect x="290" y="523" width="20" height="20" transform="rotate(0,300,533)"/><rect x="290" y="643" width="20" height="20" transform="rotate(0,300,653)"/><rect x="310" y="523" width="20" height="20" transform="rotate(0,320,533)"/><rect x="310" y="643" width="20" height="20" transform="rotate(0,320,653)"/><rect x="330" y="523" width="20" height="20" transform="rotate(0,340,533)"/><rect x="330" y="643" width="20" height="20" transform="rotate(0,340,653)"/><rect x="350" y="523" width="20" height="20" transform="rotate(0,360,533)"/><rect x="350" y="643" width="20" height="20" transform="rotate(0,360,653)"/><path d="M 370 523v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(0,380,533)"/><rect x="370" y="543" width="20" height="20" transform="rotate(0,380,553)"/><rect x="370" y="563" width="20" height="20" transform="rotate(0,380,573)"/><rect x="370" y="583" width="20" height="20" transform="rotate(0,380,593)"/><rect x="370" y="603" width="20" height="20" transform="rotate(0,380,613)"/><rect x="370" y="623" width="20" height="20" transform="rotate(0,380,633)"/><path d="M 370 643v 20h 20a 20 20, 0, 0, 0, -20 -20" transform="rotate(90,380,653)"/></clipPath><clipPath id="clip-path-corners-dot-color-0-1"><rect x="290" y="563" width="60" height="60" transform="rotate(-90,320,593)"/></clipPath></defs><defs/><path fill="#363535" d="M805.5,96.15c5.51,0,10,4.49,10,10v621h-631v-621c0-5.51,4.49-10,10-10H805.5 M805.5,66.15h-611c-22.09,0-40,17.91-40,40 v651h691v-651C845.5,84.06,827.59,66.15,805.5,66.15L805.5,66.15z"/>
			<polygon fill="#908e8e" points="154.5,789.86 0,789.86 58.69,691.61 0,593.36 154.5,593.36"/>
			<path fill="null" d="M154.5,736.04c0-37.88,0-79.17,0-103.02c0,0-53.2,20.93-53.2,48.5C101.3,736.04,154.5,736.04,154.5,736.04z"/>
			<polygon fill="#908e8e" points="845.5,789.86 1000,789.86 941.31,691.61 1000,593.36 845.5,593.36"/>
			<path fill="null" d="M845.5,736.04c0-37.88,0-79.17,0-103.02c0,0,53.2,20.93,53.2,48.5C898.7,736.04,845.5,736.04,845.5,736.04z"/>
			<path fill="#363535" d="M101.3,681.69v-3.03C101.27,679.69,101.27,680.7,101.3,681.69z"/>
			<path fill="#363535" d="M101.3,678.61v0.05C101.3,678.64,101.3,678.63,101.3,678.61L101.3,678.61z"/>
			<path fill="#363535" d="M898.7,678.66v3.03C898.73,680.7,898.73,679.69,898.7,678.66z"/>
			<path fill="#363535" d="M898.7,806.14V681.69c-1.64,53.28-100.07,45.66-154.26,45.66H516.23h-32.46H255.56c-54.18,0-152.62,7.62-154.26-45.66 l0,124.44c-0.01,0.34-0.03,0.68-0.03,1.02v66.69c0,27.61,22.39,50,50,50h104.28h228.21h32.46h228.21h104.28 c27.61,0,50-22.39,50-50v-66.69C898.72,806.81,898.7,806.48,898.7,806.14z"/>
			<text fill="#ffffff" font-size="90" x="50%" y="831" dominant-baseline="middle" text-anchor="middle" font-family="sans-serif" font-weight="600">SYNECX AI</text><rect x="215" y="128" height="571" width="570" clip-path="url('#clip-path-background-color')" fill="#ffffff"/><rect x="250" y="163" height="500" width="500" clip-path="url('#clip-path-dot-color')" fill="#000000"/><rect x="250" y="163" height="140" width="140" clip-path="url('#clip-path-corners-square-color-0-0')" fill="#000000"/><rect x="290" y="203" height="60" width="60" clip-path="url('#clip-path-corners-dot-color-0-0')" fill="#000000"/><rect x="610" y="163" height="140" width="140" clip-path="url('#clip-path-corners-square-color-1-0')" fill="#000000"/><rect x="650" y="203" height="60" width="60" clip-path="url('#clip-path-corners-dot-color-1-0')" fill="#000000"/><rect x="250" y="523" height="140" width="140" clip-path="url('#clip-path-corners-square-color-0-1')" fill="#000000"/><rect x="290" y="563" height="60" width="60" clip-path="url('#clip-path-corners-dot-color-0-1')" fill="#000000"/></svg>