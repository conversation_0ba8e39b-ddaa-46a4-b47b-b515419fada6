import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import MenuItem1 from '@/models/MenuItem1';

export async function GET(request) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    const skip = (page - 1) * limit;
    
    const menuItems = await MenuItem1.find({})
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    const total = await MenuItem1.countDocuments({});
    
    return NextResponse.json({
      menuItems,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalResults: total
      }
    });
  } catch (error) {
    console.error('Error fetching menu items:', error);
    return NextResponse.json({ 
      message: 'Error fetching menu items', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const {
      productName,
      barcode,
      price,
      quantity,
      shortCode,
      description,
      orderType
    } = body;
    
    // Validate required fields
    if (!productName || !barcode || !price || !quantity || !shortCode || !description || !orderType) {
      return NextResponse.json({ 
        error: 'All fields are required' 
      }, { status: 400 });
    }
    
    // Validate orderType
    if (!['Delivery', 'Dine-In', 'Pickup'].includes(orderType)) {
      return NextResponse.json({ 
        error: 'Invalid order type. Must be Delivery, Dine-In, or Pickup' 
      }, { status: 400 });
    }
    
    const newMenuItem = new MenuItem1({
      productName,
      barcode,
      price,
      quantity,
      shortCode,
      description,
      orderType
    });
    
    await newMenuItem.save();
    
    return NextResponse.json({ 
      message: 'Menu item created successfully', 
      menuItem: newMenuItem 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating menu item:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'Menu item with this barcode already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error creating menu item', 
      error: error.message 
    }, { status: 400 });
  }
}
