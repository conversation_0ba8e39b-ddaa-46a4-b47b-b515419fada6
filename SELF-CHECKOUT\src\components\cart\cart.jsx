import React, { useState, useCallback, useEffect,useRef  } from "react";
import {
  Container,
  Typography,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  List,
  Divider,
  ListItem,
  FormControl,
  MenuItem,
  Select,
  ListItemText,
  Autocomplete,
  CircularProgress,
} from "@mui/material"; 
import IconButton from "@mui/material/IconButton";
import DeleteIcon from "@mui/icons-material/Delete";
import { toast } from "react-toastify";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faShoppingCart } from "@fortawesome/free-solid-svg-icons";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Header from "./header";
import AdsSection from "./ads-section";

const WEIGHT_BASED_ITEMS = new Set([
  "Milk Assorted",
  "Normal Assorted",
  "Ghee Assorted",
  "Kaju Assorted",
  "ARISI-MURUKKU",
  "BOMBAY-MIXTURE",
  "GARLIC-MIXTURE",
  "KAARA-BOONTHI",
  "KAARA-MURUKKU",
  "KAI-SUTHU-MURUKKU",
  "KARA-SEV",
  "MASALA KADALAI",
  "MASALA-POTATO-CHIPS-GREEN-",
  "MASALA-POTATO-CHIPS-RED-",
  "NAENDHRAM-CHIPS",
  "NORMAL-MIXTURE",
  "OTTU-PAKKODA",
  "POTATO-CHIPS",
  "PUDI-MURUKKU",
  "THATTA-MURUKKU",
  "CORN",
  "BADHAM-BARFI",
  "BADHUSHA",
  "BANARAS-SANDWICH",
  "BESAN-LADDU",
  "BOMBAY-HALWA",
  "CARROT-MYSORE-PAK",
  "CHANDRAKALA",
  "DRY-FRUIT-LADDU",
  "GHEE-MYSORE-PAK",
  "GULAB-JAMUN",
  "GULKAN-BARFI",
  "HORLICKS-BARFI",
  "JILAPI",
  "KAJU-KATLI",
  "KAJU-PISTHA-ROLL",
  "KALA-JAMUN",
  "KALAKAN-BARFI",
  "LADDU",
  "LAMBA-JAMUN",
  "MAKAN-PEDA",
  "MANGO-KATLI",
  "MILK-CAKE",
  "MILK-PEDA",
  "MOTHI-LADDU",
  "MOTHI-PAK",
  "MYSORE-PAK",
  "RASGULLA",
  "SPECIAL-GHEE-SOANPAPDI",
]);


const Cart = ({ onUpdateTotalPrice }) => {
  const [items, setItems] = useState([]);
  const [displayedItems, setDisplayedItems] = useState([]);
  const [cartItems, setCartItems] = useState([]);

  const [openAddProductDialog, setOpenAddProductDialog] = useState(false);
  const [newProductName, setNewProductName] = useState("");
  const [newProductPrice, setNewProductPrice] = useState("");
  const [newProductQuantity, setNewProductQuantity] = useState(1);
  const [newProductShortcode, setNewProductShortcode] = useState("");
  const [productSuggestions, setProductSuggestions] = useState([]);
  const [productLoading, setProductLoading] = useState(false);
  // Fetch product suggestions for Add Product dialog
  const fetchProductSuggestions = async (query) => {
    setProductLoading(true);
    try {
      const response = await fetch(`/api/search?query=${encodeURIComponent(query)}`);
      if (!response.ok) throw new Error("Failed to fetch product suggestions");
      const data = await response.json();
      setProductSuggestions(data);
    } catch (error) {
      setProductSuggestions([]);
    } finally {
      setProductLoading(false);
    }
  };

  // When user types in product name, fetch suggestions
  const handleProductNameInput = (event, value, reason) => {
    setNewProductName(value);
    if (value && value.length > 0) {
      fetchProductSuggestions(value);
    } else {
      setProductSuggestions([]);
      setNewProductPrice("");
    }
  };

  // When user selects a suggestion, update price
  const handleProductSuggestionSelect = (event, value) => {
    if (value && value.productName) {
      setNewProductName(value.productName);
      // Prefer pricePerKg if present, else price
      setNewProductPrice(value.pricePerKg || value.price || "");
    } else if (typeof value === "string") {
      setNewProductName(value);
      setNewProductPrice("");
    }
  };
  const [weight, setWeight] = useState(0);
  const [clicked, setClicked] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [openSearchDialog, setOpenSearchDialog] = useState(false);
  const [clip_tag, setClipTag] = useState([]);

  // Motion Detection State and Refs
  const canvasRef = useRef(null);
  const [roi, setRoi] = useState({ x: 210, y: 159, width: 257, height: 337 }); // Updated ROI from cart1.jsx
  const [showMotionToast, setShowMotionToast] = useState(false);
  const [showItemToast, setShowItemToast] = useState(false);
  const motionDetectedRef = useRef(false);
  const framesSinceMotionRef = useRef(0);
  const cooldownRef = useRef(false);
  const [itemPlaced, setItemPlaced] = useState(false); // New state for item placement

// Calculate total price
const calculateTotalPrice = (items) => {
  return items.reduce((acc, item) => {
    if (item.weight_flag === 1) {
      return acc + item.price * (item.weight || 0);
    }
    return acc + item.price * (item.quantity || 1);
  }, 0);
};

// Update total price whenever cart items change
useEffect(() => {
  const totalPrice = calculateTotalPrice(cartItems);
  if (onUpdateTotalPrice) {
    onUpdateTotalPrice(totalPrice);
  }
}, [cartItems, onUpdateTotalPrice]);

  // useEffect(() => {
  //   const cellSize = 30;
  //   const diffThreshold = 10000;
  //   const processEveryNthFrame = 3;
  //   let frameCounter = 0;
  //   let previousGrayData = null;

  //   const fetchAndProcessImage = async () => {
  //     try {
  //       const response = await fetch("http://*************:9000/image");
  //       const blob = await response.blob();
  //       const url = URL.createObjectURL(blob);
  //       // const image = new Image();
  //       const image = new window.Image();
  //       image.src = url;
  //       image.onload = () => {
  //         const canvas = canvasRef.current;
  //         const ctx = canvas.getContext("2d");
  //         canvas.width = image.width;
  //         canvas.height = image.height;

  //         ctx.clearRect(0, 0, canvas.width, canvas.height);
  //         ctx.drawImage(image, 0, 0);

  //         frameCounter++;
  //         if (frameCounter % processEveryNthFrame !== 0) {
  //           URL.revokeObjectURL(url);
  //           return;
  //         }

  //         const { x: roiX, y: roiY, width, height } = roi;

  //         const currentImageData = ctx.getImageData(roiX, roiY, width, height);
  //         const currentGrayData = toGrayscale(currentImageData);

  //         if (previousGrayData) {
  //           const motionCells = detectMotion(
  //             previousGrayData,
  //             currentGrayData,
  //             width,
  //             height,
  //             cellSize,
  //             diffThreshold
  //           );

  //           const motionDetectedNow = motionCells.length > 0;
  //           if (motionDetectedNow && !cooldownRef.current) {
  //             setShowMotionToast(true);
  //             motionDetectedRef.current = true;
  //             framesSinceMotionRef.current = 0;
  //             cooldownRef.current = true;
  //             setTimeout(() => {
  //               setShowMotionToast(false);
  //               cooldownRef.current = false;
  //             }, 500);
  //           } else if (motionDetectedRef.current) {
  //             framesSinceMotionRef.current += 1;
  //             if (framesSinceMotionRef.current >= 2) {
  //               setShowItemToast(true);
  //               motionDetectedRef.current = false;
  //               framesSinceMotionRef.current = 0;
  //               setItemPlaced(true); // Set itemPlaced to true
  //               setTimeout(() => {
  //                 setShowItemToast(false);
  //               }, 1000);
  //             }
  //           }
  //         }

  //         previousGrayData = currentGrayData;
  //         URL.revokeObjectURL(url);
  //       };
  //     } catch (error) {
  //       console.error("Error fetching or processing image:", error);
  //     }
  //   };

  //   const interval = setInterval(fetchAndProcessImage, 250);
  //   return () => clearInterval(interval);
  // }, [roi]);


 //saas 
  useEffect(() => {
    const cellSize = 20; // Updated from cart1.jsx
    const diffThreshold = 12500; // Updated from cart1.jsx
    const processEveryNthFrame = 1; // Updated from cart1.jsx - process every frame
    let frameCounter = 0;
    let previousGrayData = null;

  //   
  const fetchAndProcessImage = async () => {
    try {
      const response = await fetch("http://*************:9000/image");
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      // const image = new Image();
      const image = new window.Image();
      image.src = url;
      image.onload = () => {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext("2d");
        canvas.width = image.width;
        canvas.height = image.height;

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(image, 0, 0);

        frameCounter++;
        if (frameCounter % processEveryNthFrame !== 0) {
          URL.revokeObjectURL(url);
          return;
        }

        const { x: roiX, y: roiY, width, height } = roi;

        const currentImageData = ctx.getImageData(roiX, roiY, width, height);
        const currentGrayData = toGrayscale(currentImageData);

        if (previousGrayData) {
          const motionCells = detectMotion(
            previousGrayData,
            currentGrayData,
            width,
            height,
            cellSize,
            diffThreshold
          );

          const motionDetectedNow = motionCells.length > 0;
          if (motionDetectedNow && !cooldownRef.current) {
            setShowMotionToast(true);
            motionDetectedRef.current = true;
            cooldownRef.current = true;

            // Immediately call the prediction API
            captureAndSendImage();

            setTimeout(() => {
              setShowMotionToast(false);
              cooldownRef.current = false;
            }, 300); // Updated cooldown from cart1.jsx
          }
        }

        previousGrayData = currentGrayData;
        URL.revokeObjectURL(url);
      };
    } catch (error) {
      console.error("Error fetching or processing image:", error);
    }
  };

  const interval = setInterval(fetchAndProcessImage, 250); // Updated interval from cart1.jsx
  return () => clearInterval(interval);
}, [roi]);

  useEffect(() => {
    if (itemPlaced) {
      captureAndSendImage();
      setItemPlaced(false); // Reset itemPlaced after prediction
    }
  }, [itemPlaced]);

  const toGrayscale = (imageData) => {
    const { data, width, height } = imageData;
    const grayData = new Uint8ClampedArray(width * height);
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
      grayData[i / 4] = gray;
    }
    return grayData;
  };

  const detectMotion = (prevGray, currentGray, width, height, cellSize, threshold) => {
    const motionCells = [];
    for (let y = 0; y <= height - cellSize; y += cellSize) {
      for (let x = 0; x <= width - cellSize; x += cellSize) {
        let cellDiff = 0;
        for (let i = y; i < y + cellSize; i++) {
          for (let j = x; j < x + cellSize; j++) {
            const index = i * width + j;
            if (index < prevGray.length) {
              cellDiff += Math.abs(prevGray[index] - currentGray[index]);
            }
          }
        }
        if (cellDiff > threshold) {
          motionCells.push({ x, y });
        }
      }
    }
    return motionCells;
  };


  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedItems = localStorage.getItem("cartItems");
      setCartItems(savedItems ? JSON.parse(savedItems) : []);
    }
  }, []);
  const subtotal = cartItems
    .reduce((acc, item) => {
      if (item.weight_flag === 1) {
        return acc + item.price * (item.weight || 0);
      }
      return acc + item.price * (item.quantity || 1);
    }, 0)
    .toFixed(2);


  const totalAmount = Math.round(parseFloat(subtotal));

  const handleDropdownChange = async (index, newProductName) => {
    try {
      const isWeightBased = WEIGHT_BASED_ITEMS.has(newProductName);
      const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
      const priceResponse = await fetch(
        `/api/${priceEndpoint}?productName=${encodeURIComponent(
          newProductName
        )}`
      );

      if (!priceResponse.ok) {
        throw new Error(
          `Failed to fetch price for ${newProductName}: ${priceResponse.statusText}`
        );
      }

      const priceData = await priceResponse.json();
      const newPrice = isWeightBased
        ? priceData.pricePerKg
        : priceData.pricePer;

      setCartItems((prevCartItems) => {
        const updatedCartItems = prevCartItems.map((item, i) => {
          if (i === index) {
            const updatedItem = {
              ...item,
              selectedProduct: newProductName,
              productName: newProductName,
              price: newPrice,
              weight_flag: isWeightBased ? 1 : 0,
            };

            if (item.weight_flag !== (isWeightBased ? 1 : 0)) {
              if (isWeightBased) {
                delete updatedItem.quantity;
                updatedItem.weight = 0;
              } else {
                delete updatedItem.weight;
                updatedItem.quantity = 1;
              }
            }

            return updatedItem;
          }
          return item;
        });

        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
        return updatedCartItems;
      });
    } catch (error) {
      console.error("Error updating product price:", error);
      toast.error("Failed to update product price");
    }
  };
 

  // 
  // const captureAndSendImage = useCallback(async () => {
  //   try {
  //     toast.info("Capturing image...", { autoClose: 800 });

  //     const response = await fetch("http://*************:9000/image");
  //     if (!response.ok) {
  //       throw new Error(`Image capture failed: ${response.statusText}`);
  //     }
  //     const blob = await response.blob();

  //     const formData = new FormData();
  //     formData.append("image", blob, "raspberrypi.jpg");

  //     toast.info("Sending image for prediction...", { autoClose: 800 });

  //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
  //       method: "POST",
  //       body: formData,
  //     });

  //     if (!predictResponse.ok) {
  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
  //     }

  //     const data = await predictResponse.json();
  //     console.log(data);
  //     const { yolo_tag, clip_tag, weight_flag } = data;

  //     const weightResponse = await fetch("http://*************:9000/weight");
  //     if (!weightResponse.ok) {
  //       throw new Error("Failed to fetch weight");
  //     }
  //     const weightData = await weightResponse.json();
  //     const weight = weightData.weight;

  //     setClipTag(clip_tag);
  //     console.log("Clip Tag: ", clip_tag);

  //     // Update cart items based on prediction
  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
  //     const newCartItems = await Promise.all(
  //       tags.map(async (productName, index) => {
  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
  //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
  //         const priceResponse = await fetch(
  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
  //         );

  //         if (!priceResponse.ok) {
  //           throw new Error(`Failed to fetch price for ${productName}`);
  //         }

  //         const priceData = await priceResponse.json();
  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;

  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);

  //         return {
  //           productName,
  //           weight_flag: isWeightBased ? 1 : 0,
  //           alternatives: [productName, ...alternatives],
  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
  //         };
  //       })
  //     );

  //     // Remove items not in the predict response
  //     setCartItems((prevCartItems) => {
  //       const updatedCartItems = prevCartItems.filter((item) =>
  //         tags.includes(item.productName)
  //       );

  //       // Add new items from the predict response
  //       newCartItems.forEach((newItem) => {
  //         const existingItemIndex = updatedCartItems.findIndex(
  //           (item) => item.productName === newItem.productName
  //         );

  //         if (existingItemIndex > -1) {
  //           const existingItem = updatedCartItems[existingItemIndex];
  //           if (newItem.weight_flag === 1) {
  //             updatedCartItems[existingItemIndex] = {
  //               ...existingItem,
  //               weight: (existingItem.weight || 0) + (newItem.weight || 0),
  //             };
  //           } else {
  //             updatedCartItems[existingItemIndex] = {
  //               ...existingItem,
  //               quantity: (existingItem.quantity || 1) + (newItem.quantity || 1),
  //             };
  //           }
  //         } else {
  //           updatedCartItems.push(newItem);
  //         }
  //       });

  //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
  //       return updatedCartItems;
  //     });

  //     setWeight(weight);
  //   } catch (error) {
  //     console.error("Error capturing or sending image:", error);
  //   }
  // }, []);
  



  // const captureAndSendImage = useCallback(async () => {
  //   try {
  //     toast.info("Capturing image...", { autoClose: 800 });
  
  //     const response = await fetch("http://*************:9000/image");
  //     if (!response.ok) {
  //       throw new Error(`Image capture failed: ${response.statusText}`);
  //     }
  //     const blob = await response.blob();
  
  //     const formData = new FormData();
  //     formData.append("image", blob, "raspberrypi.jpg");
  
  //     toast.info("Sending image for prediction...", { autoClose: 800 });
  
  //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
  //       method: "POST",
  //       body: formData,
  //     });
  
  //     if (!predictResponse.ok) {
  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
  //     }
  
  //     const data = await predictResponse.json();
  //     console.log(data);
  //     const { yolo_tag, clip_tag, weight_flag } = data;
  
  //     const weightResponse = await fetch("http://*************:9000/weight");
  //     if (!weightResponse.ok) {
  //       throw new Error("Failed to fetch weight");
  //     }
  //     const weightData = await weightResponse.json();
  //     const weight = weightData.weight;
  
  //     setClipTag(clip_tag);
  //     console.log("Clip Tag: ", clip_tag);
  
  //     // Process predicted items
  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
  //     const newCartItems = await Promise.all(
  //       tags.map(async (productName, index) => {
  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
  //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
  //         const priceResponse = await fetch(
  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
  //         );
  
  //         if (!priceResponse.ok) {
  //           throw new Error(`Failed to fetch price for ${productName}`);
  //         }
  
  //         const priceData = await priceResponse.json();
  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
  
  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);
  
  //         return {
  //           productName,
  //           weight_flag: isWeightBased ? 1 : 0,
  //           alternatives: [productName, ...alternatives],
  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
  //           fromPredict: true  // Mark items from prediction
  //         };
  //       })
  //     );
  
  //     // Update cart items
  //     setCartItems((prevCartItems) => {
  //       // Keep manually added items
  //       const manualItems = prevCartItems.filter(item => !item.fromPredict);
        
  //       // Merge predicted items with existing quantities/weights
  //       const predictedItems = newCartItems.map(newItem => {
  //         const existingItem = prevCartItems.find(
  //           item => item.productName === newItem.productName && item.fromPredict
  //         );
          
  //         if (existingItem) {
  //           // Preserve existing quantity/weight for previously predicted items
  //           if (newItem.weight_flag === 1) {
  //             return {
  //               ...newItem,
  //               weight: existingItem.weight
  //             };
  //           } else {
  //             return {
  //               ...newItem,
  //               quantity: existingItem.quantity
  //             };
  //           }
  //         }
  //         return newItem;
  //       });

  //       // Combine manual and predicted items
  //       const updatedCartItems = [...manualItems, ...predictedItems];
  //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
  //       return updatedCartItems;
  //     });
  
  //     setWeight(weight);
  //   } catch (error) {
  //     console.error("Error capturing or sending image:", error);
  //   }
  // }, []);


  const captureAndSendImage = useCallback(async () => {
    try {
      toast.info("Capturing image...", { autoClose: 800 });
  
      const response = await fetch("http://*************:9000/image");
      if (!response.ok) {
        throw new Error(`Image capture failed: ${response.statusText}`);
      }
      const blob = await response.blob();
  
      const formData = new FormData();
      formData.append("image", blob, "raspberrypi.jpg");
  
      toast.info("Sending image for prediction...", { autoClose: 800 });
  
      const predictResponse = await fetch("http://*************:9000/predict", {
        method: "POST",
        body: formData,
      });
  
      if (!predictResponse.ok) {
        throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
      }
  
      const data = await predictResponse.json();
      console.log(data);
      const { yolo_tag, clip_tag, weight_flag } = data;
  
      const weightResponse = await fetch("http://*************:9000/weight");
      if (!weightResponse.ok) {
        throw new Error("Failed to fetch weight");
      }
      const weightData = await weightResponse.json();
      const weight = weightData.weight;
  
      setClipTag(clip_tag);
      console.log("Clip Tag: ", clip_tag);
                           
      // Process predicted items
      const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
      
      // Calculate product counts here, after tags is defined
      const productCounts = tags.reduce((acc, product) => {
        acc[product] = (acc[product] || 0) + 1;
        return acc;
      }, {});

      const newCartItems = await Promise.all(
        tags.map(async (productName, index) => {
          const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
          const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
          const priceResponse = await fetch(
            `/api/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
          );
  
          if (!priceResponse.ok) {
            throw new Error(`Failed to fetch price for ${productName}`);
          }
  
          const priceData = await priceResponse.json();
          const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
  
          const alternatives = clip_tag[index].filter((tag) => tag !== productName);
  
          return {
            productName,
            weight_flag: isWeightBased ? 1 : 0,
            alternatives: [productName, ...alternatives],
            ...(isWeightBased 
              ? { weight: (weight || 0) * productCounts[productName], price: price || 0 } 
              : { quantity: productCounts[productName], price: price || 0 }
            ),
            fromPredict: true
          };
        })
      );
  
      // Update cart items
      setCartItems((prevCartItems) => {
        // Keep manually added items
        const manualItems = prevCartItems.filter(item => !item.fromPredict);
        
        // Get unique predicted items to avoid duplicates
        const uniquePredictedItems = Array.from(new Set(newCartItems.map(item => item.productName)))
          .map(productName => {
            const item = newCartItems.find(item => item.productName === productName);
            const existingItem = prevCartItems.find(
              prevItem => prevItem.productName === productName && prevItem.fromPredict
            );
            
            if (existingItem) {
              // Preserve existing quantity/weight for previously predicted items
              if (item.weight_flag === 1) {
                return {
                  ...item,
                  weight: existingItem.weight
                };
              } else {
                return {
                  ...item,
                  quantity: existingItem.quantity
                };
              }
            }
            return item;
          });

        // Combine manual and predicted items
        const updatedCartItems = [...manualItems, ...uniquePredictedItems];
        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
        return updatedCartItems;
      });
  
      setWeight(weight);
    } catch (error) {
      console.error("Error capturing or sending image:", error);
    }
  }, []);

// Modified addProduct function
const addProduct = (product) => {
  setCartItems((prevCartItems) => {
    const updatedCartItems = [...prevCartItems];
    const existingItemIndex = updatedCartItems.findIndex(
      (item) => item.productName === product.productName && !item.fromPredict
    );

    if (existingItemIndex === -1) {
      // Add new item without fromPredict flag
      updatedCartItems.push({
        ...product,
        fromPredict: false
      });
    } else {
      // Update existing manual item
      const existingItem = updatedCartItems[existingItemIndex];
      if (product.weight_flag === 1) {
        updatedCartItems[existingItemIndex] = {
          ...existingItem,
          weight: (existingItem.weight || 0) + (product.weight || 0)
        };
      } else {
        updatedCartItems[existingItemIndex] = {
          ...existingItem,
          quantity: (existingItem.quantity || 1) + (product.quantity || 1)
        };
      }
    }

    localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    return updatedCartItems;
  });
};
  const handleIncreaseQuantity = (productName) => {
    const cartItems = JSON.parse(localStorage.getItem("cartItems")) || [];
    const updatedCartItems = cartItems.map((item) =>
      item.productName === productName
        ? { ...item, quantity: (item.quantity || 1) + 1 }
        : item
    );
    localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    setCartItems(updatedCartItems);
  };

  const handleDecreaseQuantity = (productName) => {
    setCartItems((prevItems) => {
      const updatedCartItems = prevItems
        .map((item) => {
          if (item.productName === productName) {
            const newQuantity = (item.quantity || 1) - 1;
            return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;
          }
          return item;
        })
        .filter(Boolean);

      localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
      return updatedCartItems;
    });
  };

  const handleRemoveTag = (tagToRemove) => {
    setCartItems((prevCartItems) => {
      const indexToRemove = prevCartItems.findIndex(
        (item) => item.productName === tagToRemove
      );
      if (indexToRemove === -1) return prevCartItems;
      const updatedCartItems = [
        ...prevCartItems.slice(0, indexToRemove),
        ...prevCartItems.slice(indexToRemove + 1),
      ];
      localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));

      return updatedCartItems;
    });
  };

  const handleAddToCart = async (item) => {
    // Ensure productName is set (for search results)
    const productName = item.productName || item.name || "";
    if (!productName) {
      toast.error("Product name is missing.");
      return;
    }
    const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
    let currentWeight = 0;

    if (isWeightBased) {
      try {
        const response = await fetch("http://192.168.1.50:9000/weight");
        if (response.ok) {
          const data = await response.json();
          currentWeight = data.weight || 0;
        }
      } catch (error) {
        console.error("Error fetching weight:", error);
        toast.error("Failed to get weight from scale");
        return;
      }
    }

    let updatedCartItems;

    if (isWeightBased) {
      updatedCartItems = [
        ...cartItems,
        {
          ...item,
          productName,
          weight_flag: 1,
          alternatives: [productName],
          weight: currentWeight,
          price: item.pricePerKg || item.price || 0,
        },
      ];
      toast.info(`Added ${currentWeight}KG of ${productName} to cart`);
    } else {
      const existingItemIndex = cartItems.findIndex(
        (cartItem) =>
          cartItem.productName === productName && !cartItem.weight_flag
      );

      if (existingItemIndex > -1) {
        updatedCartItems = cartItems.map((cartItem, index) => {
          if (index === existingItemIndex) {
            const newQuantity = (cartItem.quantity || 1) + 1;
            toast.info(
              `Updated ${productName} quantity to ${newQuantity}`
            );
            return {
              ...cartItem,
              quantity: newQuantity,
              price: item.price || 0,
            };
          }
          return cartItem;
        });
      } else {
        updatedCartItems = [
          ...cartItems,
          {
            ...item,
            productName,
            weight_flag: 0,
            alternatives: [productName],
            quantity: 1,
            price: item.price || 0,
          },
        ];
        toast.info(`Added ${productName} to cart`);
      }
    }

    setCartItems(updatedCartItems);
    localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
  };

  const handleOpenAddProductDialog = () => {
    setOpenAddProductDialog(true);
  };

  const handleCloseAddProductDialog = () => {
    setOpenAddProductDialog(false);
    setNewProductName("");
    setNewProductPrice("");
    setNewProductQuantity(1);
    setNewProductShortcode("");
  };

  const handleAddProductSubmit = () => {
    if (!newProductName || !newProductPrice) {
      setError("Please enter a product name and price.");
      return;
    }

    const newProduct = {
      _id: Date.now().toString(),
      productName: newProductName,
      price: parseFloat(newProductPrice),
      quantity: newProductQuantity,
      shortcode: newProductShortcode,
    };

    setItems([...items, newProduct]);
    handleAddToCart(newProduct);
    handleCloseAddProductDialog();
  };


  // Modified scan logic
  const handleClick = () => {
    captureAndSendImage();
    setTimeout(() => setClicked(false), 600);
  };

  const handleClearCart = () => {
    setCartItems([]); // Clear cart items state
    localStorage.removeItem("cartItems"); // Remove from localStorage
    toast.success("Cart cleared successfully!"); // Optional success message
  };

  const fetchSearchResults = async (query) => {
    try {
      const response = await fetch(
        `/api/search?query=${encodeURIComponent(query)}`
      );
      if (!response.ok) throw new Error("Failed to fetch search results");

      const data = await response.json();
      const formattedResults = data.map((item) => ({
        name: item.productName, // Ensure this matches your API field
        price: item.price, // Ensure this matches your API field
      }));

      setDisplayedItems(formattedResults);
    } catch (error) {
      console.error("Error fetching search results:", error);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
    if (query.trim() === "") {
      setDisplayedItems([]);
      return;
    }
    fetchSearchResults(query);
  };

  const handleInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    handleSearch(query);
  };

  const handleWeightChange = (productName, newWeight) => {
    setCartItems((prevItems) => {
      const updatedItems = prevItems.map((item) => {
        if (item.productName === productName) {
          const weight = Math.max(
            0,
            Math.round(parseFloat(newWeight || 0) * 1000) / 1000
          );
          return {
            ...item,
            weight: weight,
          };
        }
        return item;
      });

      localStorage.setItem("cartItems", JSON.stringify(updatedItems));
      return updatedItems;
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-full mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 h-[calc(100vh-48px)]">
          {/* Left Column - Cart Details (3/5 width) */}
          <div className="lg:col-span-3 bg-white rounded-2xl shadow-xl p-6 overflow-hidden flex flex-col min-h-[600px]">
            <div className="flex items-center justify-between mb-7">
              <h2 className="text-2xl font-bold text-gray-800 flex items-center">
                <FontAwesomeIcon icon={faShoppingCart} className="mr-3 text-blue-600 text-3xl" />
                Your Cart <span className="ml-2 text-xl font-semibold text-blue-700">({cartItems.length} items)</span>
              </h2>
              <div className="flex gap-2">
                <IconButton
                  onClick={() => setOpenSearchDialog(true)}
                  color="primary"
                  size="large"
                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #1976d2', background: '#f5faff', mr: 1 }}
                >
                  <SearchOutlinedIcon style={{ fontSize: 28 }} />
                </IconButton>
                <IconButton
                  onClick={() => setOpenAddProductDialog(true)}
                  color="primary"
                  size="large"
                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #1976d2', background: '#f5faff', mr: 1 }}
                >
                  <AddOutlinedIcon style={{ fontSize: 28 }} />
                </IconButton>
                <IconButton
                  onClick={handleClearCart}
                  color="error"
                  size="large"
                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #e57373', background: '#fff5f5' }}
                >
                  <DeleteIcon style={{ fontSize: 28 }} />
                </IconButton>
              </div>
            </div>
            {/* Empty Cart State */}
            {cartItems.length === 0 && (
              <div className="flex-1 flex flex-col items-center justify-center text-gray-500 py-10">
                <FontAwesomeIcon icon={faShoppingCart} className="text-[60px] mb-5 text-gray-300" />
                <h3 className="text-xl font-bold mb-2">Your cart is empty</h3>
                <p className="text-lg text-center mb-4">Add some items to get started!</p>
                <Button
                  onClick={() => setOpenSearchDialog(true)}
                  variant="contained"
                  sx={{ fontSize: 18, px: 3, py: 2, borderRadius: 2 }}
                  startIcon={<SearchOutlinedIcon style={{ fontSize: 24 }} />}
                >
                  Browse Products
                </Button>
              </div>
            )}

            {/* Cart Items List */}
            {cartItems.length > 0 && (
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto pr-2 space-y-5 custom-scrollbar">
                  {cartItems.map((item, index) => (
                    <div
                      key={index}
                      className="bg-gray-50 rounded-2xl p-6 border border-gray-300 hover:shadow-xl transition-shadow min-h-[90px] flex flex-col justify-center"
                      style={{ minHeight: 90 }}
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1 min-w-0">
                          <FormControl size="medium" className="w-full max-w-sm">
                            <Select
                              value={item.selectedProduct || item.productName || ""}
                              onChange={(e) =>
                                handleDropdownChange(index, e.target.value)
                              }
                              displayEmpty
                              className="text-lg"
                              sx={{ fontSize: 18, minHeight: 40 }}
                            >
                              {item.alternatives &&
                                item.alternatives.map((alternative, i) => (
                                  <MenuItem key={i} value={alternative} style={{ fontSize: 18, minHeight: 32 }}>
                                    {alternative}
                                  </MenuItem>
                                ))}
                            </Select>
                          </FormControl>
                        </div>
                        <IconButton
                          onClick={() => handleRemoveTag(item.productName)}
                          size="medium"
                          className="text-red-500 hover:bg-red-50 ml-2"
                          sx={{ width: 38, height: 38 }}
                        >
                          <DeleteIcon style={{ fontSize: 22 }} />
                        </IconButton>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-5">
                          {item.weight_flag === 1 ? (
                            <div className="flex items-center space-x-2">
                              <span className="text-lg text-gray-700 font-semibold">Weight:</span>
                              <TextField
                                type="number"
                                value={parseFloat(item.weight || 0)}
                                onChange={(e) =>
                                  handleWeightChange(
                                    item.productName,
                                    e.target.value
                                  )
                                }
                                size="medium"
                                className="w-24 text-lg"
                                inputProps={{ style: { fontSize: 16, height: 32 } }}
                                slotProps={{
                                  input: {
                                    endAdornment: (
                                      <span className="text-lg text-gray-500">KG</span>
                                    ),
                                    inputProps: { min: 0, step: 0.001 },
                                  }
                                }}
                              />
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <span className="text-lg text-gray-700 font-semibold">Qty:</span>
                              <div className="flex items-center space-x-2 bg-white rounded-xl border px-2 py-1">
                                <IconButton
                                  onClick={() =>
                                    handleDecreaseQuantity(item.productName)
                                  }
                                  size="medium"
                                  className="text-red-500 hover:bg-red-50"
                                  sx={{ width: 28, height: 28 }}
                                >
                                  <RemoveIcon style={{ fontSize: 18 }} />
                                </IconButton>
                                <span className="px-3 py-1 text-lg font-bold">
                                  {item.quantity || 1}
                                </span>
                                <IconButton
                                  onClick={() =>
                                    handleIncreaseQuantity(item.productName)
                                  }
                                  size="medium"
                                  className="text-green-500 hover:bg-green-50"
                                  sx={{ width: 28, height: 28 }}
                                >
                                  <AddIcon style={{ fontSize: 18 }} />
                                </IconButton>
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className="text-lg text-gray-700 font-semibold">
                            ₹{item.price} {item.weight_flag === 1 ? '/kg' : '/item'}
                          </div>
                          <div className="text-xl font-bold text-gray-900 mt-1">
                            ₹{(
                              item.price *
                              (item.weight_flag === 1
                                ? item.weight
                                : item.quantity)
                            ).toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Cart Summary at Bottom */}
            <div className="mt-6 pt-4 border-t border-gray-300">

              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-800">Order</h3>
                <div className="flex gap-2">
                  <Button
                    onClick={handleClick}
                    variant="contained"
                    color="primary"
                    sx={{ fontSize: 14, px: 1.5, py: 1, borderRadius: 2, minWidth: 70 }}
                    startIcon={<FontAwesomeIcon icon={faShoppingCart} style={{ fontSize: 16 }} />}
                  >
                    Scan
                  </Button>
                </div>
              </div>

              <div className="space-y-2 mb-3">
                <div className="flex justify-between text-base">
                  <span className="text-gray-600 font-semibold">Items:</span>
                  <span className="font-bold">{cartItems.length}</span>
                </div>
                <div className="flex justify-between text-base">
                  <span className="text-gray-600 font-semibold">Subtotal:</span>
                  <span className="font-bold">₹{subtotal}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span className="text-blue-600">
                      {Number(totalAmount).toLocaleString("en-IN", {
                        style: "currency",
                        currency: "INR",
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 3,
                      })}
                    </span>
                  </div>
                </div>
              </div>


            </div>
          </div>

          {/* Right Column - Responsive Ads and Checkout */}
          <div className="lg:col-span-2 flex flex-col gap-4">
            {/* Ads Section - Responsive */}
            <div className="bg-white rounded-2xl shadow-xl p-4 sm:p-6 flex-1 min-h-[300px] sm:min-h-[400px] lg:min-h-[500px]">
              <AdsSection />
            </div>
            
            {/* Checkout Section - Compact */}
            <div className="bg-white rounded-2xl shadow-xl p-4">
              <div className="flex gap-3">
                <Button
                  variant="contained"
                  color="success"
                  size="medium"
                  className="flex-1"
                  sx={{ fontSize: 14, py: 1.5, borderRadius: 2, fontWeight: 'bold' }}
                  disabled={cartItems.length === 0}
                >
                  Checkout
                </Button>
                <Button
                  onClick={handleClearCart}
                  variant="outlined"
                  color="error"
                  size="medium"
                  className="flex-1"
                  sx={{ fontSize: 14, py: 1.5, borderRadius: 2, fontWeight: 'bold' }}
                  disabled={cartItems.length === 0}
                >
                  Clear Cart
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Search Dialog */}
        <Dialog
          open={openSearchDialog}
          onClose={() => setOpenSearchDialog(false)}
          fullWidth
          maxWidth="sm"
        >
          <DialogTitle>
            Search Products
            <IconButton
              edge="end"
              color="inherit"
              onClick={() => setOpenSearchDialog(false)}
              aria-label="close"
              sx={{ position: "absolute", right: 10, top: 10 }}
            >
              <DeleteIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search for a product..."
              value={searchQuery}
              onChange={handleInputChange}
              sx={{ mb: 2, fontSize: 15, pr: 0 }}
              InputProps={{
                endAdornment: (
                  searchQuery && (
                    <IconButton
                      aria-label="clear search"
                      size="small"
                      onClick={() => setSearchQuery("")}
                      edge="end"
                      sx={{ mr: 0.5 }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  )
                ),
              }}
            />
            {displayedItems.length > 0 ? (
              <List>
                {displayedItems.map((item, index) => (
                  <ListItem
                    key={index}
                    button
                    onClick={() => {
                      handleAddToCart(item);
                      setOpenSearchDialog(false);
                    }}
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      borderBottom: "1px solid #ddd",
                      padding: "8px",
                    }}
                  >
                    <Typography>{item.name}</Typography>
                    <Typography
                      sx={{
                        fontWeight: "semibold",
                        color: "#000000",
                        mt: 2,
                      }}
                    >
                      ₹{item.price}
                    </Typography>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography
                sx={{ textAlign: "center", mt: 2, color: "gray" }}
              >
                No products found.
              </Typography>
            )}
          </DialogContent>
        </Dialog>
        {/* Add Product Dialog */}
        <Dialog
          open={openAddProductDialog}
          onClose={handleCloseAddProductDialog}
          fullWidth
          maxWidth="sm"
        >
          <DialogTitle>Add a Product</DialogTitle>
          <DialogContent>
            <Autocomplete
              freeSolo
              id="productName-autocomplete"
              options={productSuggestions}
              getOptionLabel={(option) => option.productName || option.name || option || ""}
              loading={productLoading}
              value={newProductName}
              inputValue={newProductName}
              onInputChange={handleProductNameInput}
              onChange={handleProductSuggestionSelect}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Product Name"
                  variant="outlined"
                  sx={{ mt: 2, width: "100%" }}
                  required
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {productLoading ? <CircularProgress color="inherit" size={18} /> : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            <TextField
              id="productPrice"
              variant="outlined"
              label="Product Price"
              value={newProductPrice}
              onChange={(e) => setNewProductPrice(e.target.value)}
              sx={{ mt: 2, width: "100%" }}
              required
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddProductDialog}>Cancel</Button>
            <Button
              onClick={handleAddProductSubmit}
              color="primary"
              variant="contained"
            >
              Add Product
            </Button>
          </DialogActions>
        </Dialog>

        {/* Hidden canvas for motion detection */}
        <canvas ref={canvasRef} style={{ display: "none" }} />

        {/* Toast notifications with cart1.jsx styling */}
        {showMotionToast && <div className="toast show -mt-10">Motion Detected!</div>}
        {showItemToast && <div className="toast show">Item placed</div>}
        
        <style jsx>{`
          .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #333;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
          }
          
          .toast.show {
            opacity: 1;
            transform: translateY(0);
          }
        `}</style>
      </div>
    </div>
  );
};

export default Cart;


