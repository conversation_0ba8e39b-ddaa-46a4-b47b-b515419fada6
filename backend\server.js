const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const cors = require('cors');
require('dotenv').config();
const path = require('path');
const Customer = require('./models/Customer');
const Product = require('./models/Product');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');

const app = express();
const port = process.env.PORT || 5000;

app.use(cors());
app.use(bodyParser.json());

mongoose.connect(process.env.MONGODB_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.log(err));

const userRouter = require('./routes/users');
app.use('/users', userRouter);

const productRouter = require('./routes/product');
app.use('/product', productRouter);

const menuItems1Router = require('./routes/menuItems1');
app.use('/menuItems1', menuItems1Router);

const categoryRouter = require('./routes/category');
app.use('/category', categoryRouter);

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    if (file.fieldname === 'productImage') {
      cb(null, 'uploads/');
    } else if (file.fieldname === 'csvFile') {
      cb(null, 'uploads/csv/');
    }
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

const uploadDirs = ['uploads', 'uploads/csv'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});


app.get('/pricePerKg', async (req, res) => {
  try {
    const { productName } = req.query; 

    if (!productName) {
      return res.status(400).json({ error: 'productName is required' });
    }
    const product = await Product.findOne({ productName });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    if (!product.pricePerKg) {
      return res.status(400).json({ error: 'pricePerKg not applicable for this category' });
    }

    return res.status(200).json({ pricePerKg: product.pricePerKg });
  } catch (error) {
    console.error('Error fetching product:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/pricePer', async (req, res) => {
  try {
    const { productName } = req.query; 

    if (!productName) {
      return res.status(400).json({ error: 'productName is required' });
    }
    const product = await Product.findOne({ productName });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    if (!product.price) {
      return res.status(400).json({ error: 'pricePerKg not applicable for this category' });
    }

    return res.status(200).json({ pricePer: product.price });
  } catch (error) {
    console.error('Error fetching product:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/', upload.single('productImage'), async (req, res) => {
  try {
    const { productName, price, pricePerKg, quantity, productCode, description, category, supplier, selectedOption } = req.body;
    const barcode = generateBarcode(productName, price);
    const productImage = req.file ? req.file.filename : null;

    const newProduct = new Product({
      productName,
      barcode,
      price,
      pricePerKg,
      quantity,
      weight: req.body.weight || null, 
      productCode,
      description,
      category,
      supplier,
      productImage,
      selectedOption,
    });

    await newProduct.save();
    res.status(201).json({ message: 'Product added successfully', newProduct });
  } catch (err) {
    console.error('Error adding product:', err);
    res.status(400).json({ message: 'Error adding product', error: err.message });
  }
});

app.post('/import-csv', upload.single('csvFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No CSV file uploaded' });
  }

  const results = [];
  const errors = [];

  fs.createReadStream(req.file.path)
    .pipe(csv())
    .on('data', async (data) => {
      try {
        results.push(data);
      } catch (error) {
        errors.push({ row: data, error: error.message });
      }
    })
    .on('end', async () => {
      try {
        for (const row of results) {
          const barcode = row.Barcode || '';
          const productCode = row['Product Code'] || '';
          const productName = row['Product Name'] || '';
          const category = row.Category || '';
          const price = parseFloat(row.Price) || 0;
          const unit = row.Unit || '';
          const supplier = row.Supplier || '';
          const quantity = parseFloat(row.Quantity) || 0;
          const status = row.Status || 'In Stock';

          let product = await Product.findOne({ barcode: barcode });

          if (product) {
            await Product.updateOne(
              { barcode: barcode },
              {
                productName,
                productCode,
                category,
                price,
                unit,
                supplier,
                quantity,
                status
              }
            );
          } else {
            const newProduct = new Product({
              barcode,
              productCode,
              productName,
              category,
              price,
              unit,
              supplier,
              quantity,
              status
            });
            await newProduct.save();
          }
        }

        fs.unlinkSync(req.file.path);

        res.status(200).json({
          message: 'CSV import completed successfully',
          totalProcessed: results.length,
          errors: errors
        });
      } catch (error) {
        console.error('Error processing CSV:', error);
        res.status(500).json({ message: 'Error processing CSV file', error: error.message });
      }
    });
});

const PrinterSchema = new mongoose.Schema({
  companyName: { type: String, required: true },
  address: { type: String, required: true },
  address1: String,
  address2: String,
  mobileNumber: { type: String, required: true },
  gstNumber: { type: String, required: true }
});

const Printer = mongoose.model('Printer', PrinterSchema);

app.post('/submit-form', async (req, res) => {
  const { companyName, address, address1, address2, mobileNumber, gstNumber } = req.body;

  try {
    const newPrinter = new Printer({
      companyName,
      address,
      address1,
      address2,
      mobileNumber,
      gstNumber,
    });

    await newPrinter.save();

    res.status(200).json({ message: 'Form data saved successfully!' });
  } catch (error) {
    console.error('Error saving form data:', error);
    res.status(500).json({ message: 'Error saving data' });
  }
});

app.get('/get-form-data', async (req, res) => {
  try {
    const printers = await Printer.find();
    res.status(200).json(printers);
  } catch (error) {
    console.error('Error fetching form data:', error);
    res.status(500).json({ message: 'Error fetching data' });
  }
});

const dailyBillCountSchema = new mongoose.Schema({
  date: { type: String, required: true }, 
  count: { type: Number, default: 1 },
});

const DailyBillCount = mongoose.model('DailyBillCount', dailyBillCountSchema);

const billSchema = new mongoose.Schema({
  customerID: String,
  customerNumber: String,
  cartItems: Array,
  totalCost: Number,
  taxAmount: Number,
  dateTime: Date,
  paymentMethod:  String,
  userEmail:  String,
  status:  String,
  cashier: String,
  mode: String,
  billno: Number,
});

const Bill = mongoose.model('Bill', billSchema);

app.post('/bills', async (req, res) => {
  const { customerID, customerNumber, cartItems, totalCost, taxAmount, dateTime, paymentMethod, userEmail, status, cashier, mode } = req.body;

  try {
    const today = new Date().toISOString().split('T')[0];

    let dailyBillCount = await DailyBillCount.findOne({ date: today });

    if (!dailyBillCount) {
      dailyBillCount = new DailyBillCount({ date: today, count: 1 });
    } else {
      dailyBillCount.count += 1;
    }

    await dailyBillCount.save();

    const billno = dailyBillCount.count;

    const newBill = new Bill({
      customerID,
      customerNumber,
      cartItems,
      totalCost,
      taxAmount,
      dateTime,
      paymentMethod,
      status,
      userEmail,
      cashier,
      mode,
      billno,
    });

    await newBill.save();

    res.status(201).json({
      message: 'Bill and customer data saved successfully',
      billno, 

    });
  } catch (error) {
    console.error('Error saving bill:', error);
    res.status(500).json({ error: 'Error saving bill' });
  }
});

app.post('/sales-summary', async (req, res) => {
  const { fromDate, toDate } = req.body;

  try {
    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);
    endDate.setHours(23, 59, 59, 999);
    console.log(startDate);
    console.log(endDate);
    
    let bills = await Bill.find({
      dateTime: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    const totalCost = Math.round(bills.reduce((sum, bill) => sum + bill.totalCost, 0));

    const allItems = [];
    bills.forEach((bill) => allItems.push(...bill.cartItems));

    const itemSales = allItems.reduce((acc, item) => {
      const key = item.productName;
      const quantity = item.quantity;
      const weight = item.weight;
      
      if (!acc[key]) {
        acc[key] = { totalSold: 0 };
      }
      acc[key].totalSold += quantity || weight;
      return acc;
    }, {});

    const sortedItems = Object.entries(itemSales).map(([name, data]) => ({
      productName: name,
      totalSold: Math.round(data.totalSold),
    }));

    const sortedBySales = [...sortedItems].sort((a, b) => b.totalSold - a.totalSold);

    const top10HighestSelling = sortedBySales.slice(0, 10);
    const top10LowestSelling = sortedBySales.slice(-10);

    const dailyTotals = bills.reduce((acc, bill) => {
      const date = bill.dateTime.toISOString().split('T')[0]; 
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date] += bill.totalCost;
      return acc;
    }, {});

    const dailyTotalArray = Object.entries(dailyTotals).map(([date, total]) => ({
      date,
      total: Math.round(total),
    }));

    const paymentMethodsTotals = bills.reduce((acc, bill) => {
      const paymentMethod = bill.paymentMethod || 'Unknown'; 
      if (!acc[paymentMethod]) {
        acc[paymentMethod] = 0;
      }
      acc[paymentMethod] += bill.totalCost;
      return acc;
    }, {});

    const paymentMethodsArray = Object.entries(paymentMethodsTotals).map(
      ([method, total]) => ({
        method,
        total: Math.round(total),
      })
    );

    const customerDetails = bills.map((bill) => ({
      customerID: bill.customerID,
      totalCost: Math.round(bill.totalCost),
    }));


    res.status(200).json({
      totalCost,
      top10HighestSelling,
      top10LowestSelling,
      dailyTotals: dailyTotalArray,
      paymentMethods: paymentMethodsArray,
      customers: customerDetails, 
    });
  } catch (error) {
    console.error('Error fetching sales summary:', error);
    res.status(500).json({ error: 'Error fetching sales summary' });
  }
});
app.get('/search', async (req, res) => {
  try {
    const { query, page = 1, limit = 10 } = req.query;
    if (!query || query.trim() === '') {
      return res.status(400).json({ error: 'Search query is required' });
    }
    const skip = (page - 1) * limit;
    const products = await Product.find({
      productName: { $regex: `^${query}`, $options: 'i' }
    })
      .skip(skip)
      .limit(parseInt(limit));
    const total = await Product.countDocuments({
      productName: { $regex: `^${query}`, $options: 'i' }
    });
    res.json({
      products,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalResults: total
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({ 
      error: 'Error searching for products',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined 
    });
  }
});
// app.get('/search', async (req, res) => {
//   try {
//     const query = req.query.query;
//     const products = await Product.find({
//       $or: [
//         { shortCode: query },
//         { barcode: query },
//         { productName: { $regex: query, $options: 'i' } }
//       ]
//     });
//     res.json(products);
//   } catch (error) {
//     res.status(500).json({ error: 'Error searching for products' });
//   }
// });



app.get('/bills-week', async (req, res) => {
  try {
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay()); 
    startOfWeek.setHours(0, 0, 0, 0); 

    const endOfWeek = new Date();
    endOfWeek.setDate(endOfWeek.getDate() + (6 - endOfWeek.getDay())); 
    endOfWeek.setHours(23, 59, 59, 999); 

    const startOfLastWeek = new Date(startOfWeek);
    startOfLastWeek.setDate(startOfLastWeek.getDate() - 7); 

    const endOfLastWeek = new Date(endOfWeek);
    endOfLastWeek.setDate(endOfLastWeek.getDate() - 7); 

    const billsThisWeekCount = await Bill.countDocuments({
      dateTime: { $gte: startOfWeek, $lte: endOfWeek },
    });

    const billsLastWeekCount = await Bill.countDocuments({
      dateTime: { $gte: startOfLastWeek, $lte: endOfLastWeek },
    });

    let percentageChange = 0;
    if (billsLastWeekCount === 0) {
      percentageChange = billsThisWeekCount > 0 ? 100 : 0;
    } else {
      percentageChange = Math.round(((billsThisWeekCount - billsLastWeekCount) / billsLastWeekCount) * 100);
    }

    res.status(200).json({
      billsThisWeekCount,
      percentageChange,
    });
  } catch (error) {
    console.error('Error fetching bill counts:', error);
    res.status(500).send('Error fetching bill counts');
  }
});

app.get('/today', async (req, res) => {
  try {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const totalToday = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: todayStart,
            $lte: todayEnd
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);

    res.status(200).json({ total: totalToday[0] ? totalToday[0].total : 0 });
  } catch (error) {
    console.error('Error fetching total for today:', error);
    res.status(500).send('Error fetching total for today');
  }
});

app.get('/week', async (req, res) => {
  try {
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date();
    endOfWeek.setDate(endOfWeek.getDate() + (6 - endOfWeek.getDay()));
    endOfWeek.setHours(23, 59, 59, 999);

    const totalThisWeek = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: startOfWeek,
            $lte: endOfWeek
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);

    res.status(200).json({ total: totalThisWeek[0] ? totalThisWeek[0].total : 0 });
  } catch (error) {
    console.error('Error fetching total for this week:', error);
    res.status(500).send('Error fetching total for this week');
  }
});

app.get('/week-percentage', async (req, res) => {
  try {
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date();
    endOfWeek.setDate(endOfWeek.getDate() + (6 - endOfWeek.getDay()));
    endOfWeek.setHours(23, 59, 59, 999);

    const startOfPreviousWeek = new Date(startOfWeek);
    startOfPreviousWeek.setDate(startOfPreviousWeek.getDate() - 7);
    const endOfPreviousWeek = new Date(endOfWeek);
    endOfPreviousWeek.setDate(endOfPreviousWeek.getDate() - 7);
    const totalThisWeek = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: startOfWeek,
            $lte: endOfWeek
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);
    const totalPreviousWeek = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: startOfPreviousWeek,
            $lte: endOfPreviousWeek
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);
    const totalCurrentWeek = totalThisWeek[0] ? totalThisWeek[0].total : 0;
    const totalLastWeek = totalPreviousWeek[0] ? totalPreviousWeek[0].total : 0;

    let percentageChange = 0;
    if (totalLastWeek > 0) {
      percentageChange = Math.round(((totalCurrentWeek - totalLastWeek) / totalLastWeek) * 100);
    }
    res.status(200).json({
      percentageChange: percentageChange
    });
  } catch (error) {
    console.error('Error calculating percentage change:', error);
    res.status(500).send('Error calculating percentage change');
  }
});

app.get('/month', async (req, res) => {
  try {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);
    endOfMonth.setHours(23, 59, 59, 999);

    const totalThisMonth = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: startOfMonth,
            $lte: endOfMonth
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);

    res.status(200).json({ total: totalThisMonth[0] ? totalThisMonth[0].total : 0 });
  } catch (error) {
    console.error('Error fetching total for this month:', error);
    res.status(500).send('Error fetching total for this month');
  }
});

app.get('/year', async (req, res) => {
  try {
    const startOfYear = new Date(new Date().getFullYear(), 0, 1);
    startOfYear.setHours(0, 0, 0, 0);

    const endOfYear = new Date(new Date().getFullYear() + 1, 0, 0);
    endOfYear.setHours(23, 59, 59, 999);

    const totalThisYear = await Bill.aggregate([
      {
        $match: {
          dateTime: {
            $gte: startOfYear,
            $lte: endOfYear
          }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$totalCost" }
        }
      }
    ]);

    res.status(200).json({ total: totalThisYear[0] ? totalThisYear[0].total : 0 });
  } catch (error) {
    console.error('Error fetching total for this year:', error);
    res.status(500).send('Error fetching total for this year');
  }
});


app.get('/customers/count', async (req, res) => {
  try {
    const customerCount = await Customer.countDocuments(); 
    res.status(200).json({ count: customerCount });
  } catch (error) {
    console.error('Error counting customers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});


app.get('/customer/:customerID', async (req, res) => {
  const { customerID } = req.params;

  try {
    const customer = await Bill.findOne({ customerID });
    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' });
    }
    res.status(200).json(customer);
  } catch (error) {
    console.error('Error retrieving customer:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/customers', async (req, res) => {
  try {
    const allCustomers = await Bill.find(); 
    res.json(allCustomers);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching customers', error });
  }
});

app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

app.get('/ping', (req, res) => {
  res.status(200).send({ status: 'Backend online' });
});

app.get('/mongodb-status', (req, res) => {
  mongoose.connection.readyState === 1
    ? res.status(200).send({ status: 'MongoDB connected' })
    : res.status(500).send({ status: 'MongoDB disconnected' });
});

app.listen(port, () => {
  console.log(`Server is running on port: ${port}`);
});
