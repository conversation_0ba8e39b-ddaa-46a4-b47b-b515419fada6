import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Category from '@/models/Category';

export async function GET(request) {
  try {
    await dbConnect();
    
    const categories = await Category.find({}).sort({ created_at: -1 });
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { category_name } = body;
    
    if (!category_name) {
      return NextResponse.json({ error: 'Category name is required' }, { status: 400 });
    }
    
    const newCategory = new Category({
      category_name,
    });
    
    await newCategory.save();
    
    return NextResponse.json({ 
      message: 'Category added successfully', 
      category: newCategory 
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding category:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'Category already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error adding category', 
      error: error.message 
    }, { status: 400 });
  }
}
