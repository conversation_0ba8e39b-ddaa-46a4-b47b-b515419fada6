import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import dbConnect from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(request) {
  try {
    await dbConnect();
    
    const users = await User.find({}).select('-password').sort({ createdAt: -1 });
    
    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { username, email, password, mobileNumber, role, storename } = body;
    
    // Validate required fields
    if (!username || !email || !password || !mobileNumber || !role || !storename) {
      return NextResponse.json({ error: 'All fields are required' }, { status: 400 });
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
    }
    
    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    // Create new user
    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      mobileNumber,
      role,
      storename,
    });
    
    await newUser.save();
    
    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser.toObject();
    
    return NextResponse.json({ 
      message: 'User created successfully', 
      user: userWithoutPassword 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'User with this email already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error creating user', 
      error: error.message 
    }, { status: 400 });
  }
}
