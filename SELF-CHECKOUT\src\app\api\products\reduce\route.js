import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';

export async function PATCH(request) {
    try {
        await connectDB();
        
        const { productName, quantity, weight } = await request.json();
        
        if (!productName) {
            return Response.json(
                { success: false, message: 'Product name is required' },
                { status: 400 }
            );
        }
        
        const product = await Product.findOne({ productName });
        if (!product) {
            return Response.json(
                { success: false, message: 'Product not found' },
                { status: 404 }
            );
        }
        
        // Reduce quantity and/or weight
        if (quantity !== undefined) {
            product.quantity = Math.max(0, (product.quantity || 0) - quantity);
        }
        if (weight !== undefined) {
            product.weight = Math.max(0, (product.weight || 0) - weight);
        }
        
        await product.save();
        
        return Response.json({
            success: true,
            message: 'Product inventory updated successfully',
            product
        });
    } catch (error) {
        console.error('Reduce inventory error:', error);
        return Response.json(
            { success: false, message: 'Error updating inventory', error: error.message },
            { status: 500 }
        );
    }
}
