import mongoose from 'mongoose';

const CustomerSchema = new mongoose.Schema({
  customerID: { type: String, required: true },
  customerNumber: { type: String },
  dateTime: { type: Date, required: true },
  cashier: { type: String, required: true },
  mode: { type: String, required: true },
}, {
  timestamps: true,
});

export default mongoose.models.Customer || mongoose.model('Customer', CustomerSchema);
