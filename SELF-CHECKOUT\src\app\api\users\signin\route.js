import { connectDB } from '@/lib/mongodb';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import User from '@/models/User';

export async function POST(request) {
    try {
        await connectDB();
        
        const { email, password } = await request.json();
        
        // Find user by email
        const user = await User.findOne({ email });
        if (!user) {
            return Response.json(
                { success: false, message: 'Invalid email or password' },
                { status: 400 }
            );
        }
        
        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return Response.json(
                { success: false, message: 'Invalid email or password' },
                { status: 400 }
            );
        }
        
        // Generate JWT token
        const token = jwt.sign(
            { 
                id: user._id, 
                username: user.username, 
                role: user.role, 
                storename: user.storename 
            },
            'your_jwt_secret',
            { expiresIn: '1h' }
        );
        
        console.log("Logged User Name: ", user.username);
        
        return Response.json({
            success: true,
            message: 'Sign in successful',
            token,
            username: user.username,
            role: user.role,
            storename: user.storename
        });
    } catch (error) {
        console.error('Signin error:', error);
        return Response.json(
            { success: false, message: 'Error: ' + error.message },
            { status: 500 }
        );
    }
}
