const router = require('express').Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

router.route('/signup').post(async (req, res) => {
  try {
    const { username, email, password, mobileNumber, role, storename } = req.body;

    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = new User({ username, email, password: hashedPassword, mobileNumber, role, storename });

    await newUser.save();
    res.status(201).json('User registered!');
  } catch (err) {
    res.status(400).json('Error: ' + err);
  }
});

router.route('/signin').post(async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json('Invalid email or password');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(400).json('Invalid email or password');
    }

    const token = jwt.sign({ id: user._id, username: user.username, role: user.role, storename: user.storename }, 'your_jwt_secret', { expiresIn: '1h' });

    res.status(200).json({ message: 'Sign in successful', token, username: user.username, role: user.role, storename: user.storename });
    console.log("Logged User Name: ", user.username);
  } catch (err) {
    res.status(500).json('Error: ' + err);
  }
});

router.route('/users').get(async (req, res) => {
  try {
    const users = await User.find();
    res.status(200).json(users);
  } catch (err) {
    res.status(400).json('Error: ' + err);
  }
});

router.use((req, res, next) => {
  req.user = { _id: 'user-id' };
  next();
});

router.post('/users/profile', async (req, res) => {
  try {
    const { username, email, storename, role } = req.body;
    const userId = req.user._id;

    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { username, email, storename, role },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({ message: 'Profile updated successfully!' });
  } catch (err) {
    res.status(400).json({ message: 'Error: ' + err });
  }
});

module.exports = router;
