(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_f54f6f._.js", {

"[project]/src/components/cart/header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-client] (ecmascript)");
;
;
const Header = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-20 sticky top-0 flex justify-center items-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            src: '/Logo.svg',
            alt: '',
            width: 200,
            height: 100
        }, void 0, false, {
            fileName: "[project]/src/components/cart/header.tsx",
            lineNumber: 6,
            columnNumber: 80
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart/header.tsx",
        lineNumber: 6,
        columnNumber: 5
    }, this);
};
_c = Header;
const __TURBOPACK__default__export__ = Header;
var _c;
__turbopack_refresh__.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/cart/ads-section.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
;
const AdsSection = ()=>{
    _s();
    const [currentAdIndex, setCurrentAdIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const ads = [
        {
            image: "/cake.jpg",
            title: "Fresh Cakes",
            subtitle: "Delicious & Fresh Daily",
            alt: "Fresh Cakes"
        },
        {
            image: "/brownie.jpg",
            title: "Chocolate Brownies",
            subtitle: "Rich & Fudgy",
            alt: "Chocolate Brownies"
        },
        {
            image: "/choc.jpg",
            title: "Premium Chocolates",
            subtitle: "Sweet Indulgence",
            alt: "Premium Chocolates"
        },
        {
            image: "/icecreamV.jpg",
            title: "Ice Cream Varieties",
            subtitle: "Cool & Refreshing",
            alt: "Ice Cream Varieties"
        },
        {
            image: "/rmcake.jpg",
            title: "Special Cakes",
            subtitle: "Made with Love",
            alt: "Special Cakes"
        },
        {
            image: "/sweets.png",
            title: "Traditional Sweets",
            subtitle: "Authentic Flavors",
            alt: "Traditional Sweets"
        },
        {
            image: "/brow.jpg",
            title: "Bakery Items",
            subtitle: "Fresh Baked Goods",
            alt: "Bakery Items"
        },
        {
            image: "/rama.jpg",
            title: "Special Offers",
            subtitle: "Limited Time Deals",
            alt: "Special Offers"
        }
    ];
    // Auto-change ads every 5 seconds
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AdsSection.useEffect": ()=>{
            const interval = setInterval({
                "AdsSection.useEffect.interval": ()=>{
                    setCurrentAdIndex({
                        "AdsSection.useEffect.interval": (prevIndex)=>(prevIndex + 1) % ads.length
                    }["AdsSection.useEffect.interval"]);
                }
            }["AdsSection.useEffect.interval"], 5000);
            return ({
                "AdsSection.useEffect": ()=>clearInterval(interval)
            })["AdsSection.useEffect"];
        }
    }["AdsSection.useEffect"], [
        ads.length
    ]);
    const currentAd = ads[currentAdIndex];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "lg:col-span-4 bg-white rounded-xl shadow-lg overflow-hidden h-full",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative h-full w-full overflow-hidden",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: currentAd.image,
                    alt: currentAd.alt,
                    className: "w-full h-full object-cover transition-all duration-1000 ease-in-out"
                }, void 0, false, {
                    fileName: "[project]/src/components/cart/ads-section.jsx",
                    lineNumber: 72,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex flex-col justify-end p-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-white",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-4xl font-bold mb-4 transition-all duration-500 ease-in-out",
                                    children: currentAd.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/ads-section.jsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-2xl mb-6 transition-all duration-500 ease-in-out",
                                    children: currentAd.subtitle
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/ads-section.jsx",
                                    lineNumber: 84,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/ads-section.jsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex space-x-2 mb-4",
                            children: ads.map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `h-2 w-8 rounded-full transition-all duration-300 ${index === currentAdIndex ? 'bg-white' : 'bg-white/30'}`
                                }, index, false, {
                                    fileName: "[project]/src/components/cart/ads-section.jsx",
                                    lineNumber: 92,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/cart/ads-section.jsx",
                            lineNumber: 90,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-lg text-white/80 font-medium",
                                children: "Powered by Synecx AI"
                            }, void 0, false, {
                                fileName: "[project]/src/components/cart/ads-section.jsx",
                                lineNumber: 103,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/cart/ads-section.jsx",
                            lineNumber: 102,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/cart/ads-section.jsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/cart/ads-section.jsx",
            lineNumber: 71,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart/ads-section.jsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
};
_s(AdsSection, "tg4uWJikuoQPXVwybbb7VTUyfzc=");
_c = AdsSection;
const __TURBOPACK__default__export__ = AdsSection;
var _c;
__turbopack_refresh__.register(_c, "AdsSection");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/cart/cart.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@fortawesome/react-fontawesome/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/cart/header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$ads$2d$section$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/cart/ads-section.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@fortawesome/free-solid-svg-icons/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Lock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/material/IconButton/IconButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/SearchOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/AddOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$LockOpen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/LockOpen.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Delete.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Button/Button.js [app-client] (ecmascript) <export default as Button>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/FormControl/FormControl.js [app-client] (ecmascript) <export default as FormControl>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Select/Select.js [app-client] (ecmascript) <export default as Select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/MenuItem/MenuItem.js [app-client] (ecmascript) <export default as MenuItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Remove.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Add$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/Add.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/TextField/TextField.js [app-client] (ecmascript) <export default as TextField>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Dialog/Dialog.js [app-client] (ecmascript) <export default as Dialog>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogTitle/DialogTitle.js [app-client] (ecmascript) <export default as DialogTitle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogContent/DialogContent.js [app-client] (ecmascript) <export default as DialogContent>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$List$2f$List$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/List/List.js [app-client] (ecmascript) <export default as List>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/ListItem/ListItem.js [app-client] (ecmascript) <export default as ListItem>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Typography/Typography.js [app-client] (ecmascript) <export default as Typography>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Autocomplete$2f$Autocomplete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Autocomplete$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/Autocomplete/Autocomplete.js [app-client] (ecmascript) <locals> <export default as Autocomplete>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CircularProgress$2f$CircularProgress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CircularProgress$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/CircularProgress/CircularProgress.js [app-client] (ecmascript) <export default as CircularProgress>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogActions$2f$DialogActions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogActions$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/DialogActions/DialogActions.js [app-client] (ecmascript) <export default as DialogActions>");
;
var _s = __turbopack_refresh__.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const WEIGHT_BASED_ITEMS = new Set([
    "Milk Assorted",
    "Normal Assorted",
    "Ghee Assorted",
    "Kaju Assorted",
    "ARISI-MURUKKU",
    "BOMBAY-MIXTURE",
    "GARLIC-MIXTURE",
    "KAARA-BOONTHI",
    "KAARA-MURUKKU",
    "KAI-SUTHU-MURUKKU",
    "KARA-SEV",
    "MASALA KADALAI",
    "MASALA-POTATO-CHIPS-GREEN-",
    "MASALA-POTATO-CHIPS-RED-",
    "NAENDHRAM-CHIPS",
    "NORMAL-MIXTURE",
    "OTTU-PAKKODA",
    "POTATO-CHIPS",
    "PUDI-MURUKKU",
    "THATTA-MURUKKU",
    "CORN",
    "BADHAM-BARFI",
    "BADHUSHA",
    "BANARAS-SANDWICH",
    "BESAN-LADDU",
    "BOMBAY-HALWA",
    "CARROT-MYSORE-PAK",
    "CHANDRAKALA",
    "DRY-FRUIT-LADDU",
    "GHEE-MYSORE-PAK",
    "GULAB-JAMUN",
    "GULKAN-BARFI",
    "HORLICKS-BARFI",
    "JILAPI",
    "KAJU-KATLI",
    "KAJU-PISTHA-ROLL",
    "KALA-JAMUN",
    "KALAKAN-BARFI",
    "LADDU",
    "LAMBA-JAMUN",
    "MAKAN-PEDA",
    "MANGO-KATLI",
    "MILK-CAKE",
    "MILK-PEDA",
    "MOTHI-LADDU",
    "MOTHI-PAK",
    "MYSORE-PAK",
    "RASGULLA",
    "SPECIAL-GHEE-SOANPAPDI"
]);
const Cart = ({ onUpdateTotalPrice })=>{
    _s();
    const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [displayedItems, setDisplayedItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [cartItems, setCartItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isCartLocked, setIsCartLocked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isAddMode, setIsAddMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [openAddProductDialog, setOpenAddProductDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [newProductName, setNewProductName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [newProductPrice, setNewProductPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [newProductQuantity, setNewProductQuantity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [newProductShortcode, setNewProductShortcode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [productSuggestions, setProductSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [productLoading, setProductLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Fetch product suggestions for Add Product dialog
    const fetchProductSuggestions = async (query)=>{
        setProductLoading(true);
        try {
            const response = await fetch(`/api/search?query=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error("Failed to fetch product suggestions");
            const data = await response.json();
            setProductSuggestions(data);
        } catch (error) {
            setProductSuggestions([]);
        } finally{
            setProductLoading(false);
        }
    };
    // When user types in product name, fetch suggestions
    const handleProductNameInput = (event, value, reason)=>{
        setNewProductName(value);
        if (value && value.length > 0) {
            fetchProductSuggestions(value);
        } else {
            setProductSuggestions([]);
            setNewProductPrice("");
        }
    };
    // When user selects a suggestion, update price
    const handleProductSuggestionSelect = (event, value)=>{
        if (value && value.productName) {
            setNewProductName(value.productName);
            // Prefer pricePerKg if present, else price
            setNewProductPrice(value.pricePerKg || value.price || "");
        } else if (typeof value === "string") {
            setNewProductName(value);
            setNewProductPrice("");
        }
    };
    const [weight, setWeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [clicked, setClicked] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchOpen, setSearchOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [openSearchDialog, setOpenSearchDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [clip_tag, setClipTag] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Motion Detection State and Refs
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [roi, setRoi] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        x: 210,
        y: 159,
        width: 257,
        height: 337
    }); // Updated ROI from cart1.jsx
    const [showMotionToast, setShowMotionToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showItemToast, setShowItemToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const motionDetectedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const framesSinceMotionRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const cooldownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const [itemPlaced, setItemPlaced] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false); // New state for item placement
    // Calculate total price
    const calculateTotalPrice = (items)=>{
        return items.reduce((acc, item)=>{
            if (item.weight_flag === 1) {
                return acc + item.price * (item.weight || 0);
            }
            return acc + item.price * (item.quantity || 1);
        }, 0);
    };
    // Update total price whenever cart items change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Cart.useEffect": ()=>{
            const totalPrice = calculateTotalPrice(cartItems);
            if (onUpdateTotalPrice) {
                onUpdateTotalPrice(totalPrice);
            }
        }
    }["Cart.useEffect"], [
        cartItems,
        onUpdateTotalPrice
    ]);
    // useEffect(() => {
    //   const cellSize = 30;
    //   const diffThreshold = 10000;
    //   const processEveryNthFrame = 3;
    //   let frameCounter = 0;
    //   let previousGrayData = null;
    //   const fetchAndProcessImage = async () => {
    //     try {
    //       const response = await fetch("http://*************:9000/image");
    //       const blob = await response.blob();
    //       const url = URL.createObjectURL(blob);
    //       // const image = new Image();
    //       const image = new window.Image();
    //       image.src = url;
    //       image.onload = () => {
    //         const canvas = canvasRef.current;
    //         const ctx = canvas.getContext("2d");
    //         canvas.width = image.width;
    //         canvas.height = image.height;
    //         ctx.clearRect(0, 0, canvas.width, canvas.height);
    //         ctx.drawImage(image, 0, 0);
    //         frameCounter++;
    //         if (frameCounter % processEveryNthFrame !== 0) {
    //           URL.revokeObjectURL(url);
    //           return;
    //         }
    //         const { x: roiX, y: roiY, width, height } = roi;
    //         const currentImageData = ctx.getImageData(roiX, roiY, width, height);
    //         const currentGrayData = toGrayscale(currentImageData);
    //         if (previousGrayData) {
    //           const motionCells = detectMotion(
    //             previousGrayData,
    //             currentGrayData,
    //             width,
    //             height,
    //             cellSize,
    //             diffThreshold
    //           );
    //           const motionDetectedNow = motionCells.length > 0;
    //           if (motionDetectedNow && !cooldownRef.current) {
    //             setShowMotionToast(true);
    //             motionDetectedRef.current = true;
    //             framesSinceMotionRef.current = 0;
    //             cooldownRef.current = true;
    //             setTimeout(() => {
    //               setShowMotionToast(false);
    //               cooldownRef.current = false;
    //             }, 500);
    //           } else if (motionDetectedRef.current) {
    //             framesSinceMotionRef.current += 1;
    //             if (framesSinceMotionRef.current >= 2) {
    //               setShowItemToast(true);
    //               motionDetectedRef.current = false;
    //               framesSinceMotionRef.current = 0;
    //               setItemPlaced(true); // Set itemPlaced to true
    //               setTimeout(() => {
    //                 setShowItemToast(false);
    //               }, 1000);
    //             }
    //           }
    //         }
    //         previousGrayData = currentGrayData;
    //         URL.revokeObjectURL(url);
    //       };
    //     } catch (error) {
    //       console.error("Error fetching or processing image:", error);
    //     }
    //   };
    //   const interval = setInterval(fetchAndProcessImage, 250);
    //   return () => clearInterval(interval);
    // }, [roi]);
    //saas 
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Cart.useEffect": ()=>{
            const cellSize = 20; // Updated from cart1.jsx
            const diffThreshold = 12500; // Updated from cart1.jsx
            const processEveryNthFrame = 1; // Updated from cart1.jsx - process every frame
            let frameCounter = 0;
            let previousGrayData = null;
            //   
            const fetchAndProcessImage = {
                "Cart.useEffect.fetchAndProcessImage": async ()=>{
                    try {
                        const response = await fetch("http://*************:9000/image");
                        const blob = await response.blob();
                        const url = URL.createObjectURL(blob);
                        // const image = new Image();
                        const image = new window.Image();
                        image.src = url;
                        image.onload = ({
                            "Cart.useEffect.fetchAndProcessImage": ()=>{
                                const canvas = canvasRef.current;
                                const ctx = canvas.getContext("2d");
                                canvas.width = image.width;
                                canvas.height = image.height;
                                ctx.clearRect(0, 0, canvas.width, canvas.height);
                                ctx.drawImage(image, 0, 0);
                                frameCounter++;
                                if (frameCounter % processEveryNthFrame !== 0) {
                                    URL.revokeObjectURL(url);
                                    return;
                                }
                                const { x: roiX, y: roiY, width, height } = roi;
                                const currentImageData = ctx.getImageData(roiX, roiY, width, height);
                                const currentGrayData = toGrayscale(currentImageData);
                                if (previousGrayData) {
                                    const motionCells = detectMotion(previousGrayData, currentGrayData, width, height, cellSize, diffThreshold);
                                    const motionDetectedNow = motionCells.length > 0;
                                    if (motionDetectedNow && !cooldownRef.current) {
                                        setShowMotionToast(true);
                                        motionDetectedRef.current = true;
                                        cooldownRef.current = true;
                                        // Immediately call the prediction API
                                        captureAndSendImage();
                                        setTimeout({
                                            "Cart.useEffect.fetchAndProcessImage": ()=>{
                                                setShowMotionToast(false);
                                                cooldownRef.current = false;
                                            }
                                        }["Cart.useEffect.fetchAndProcessImage"], 300); // Updated cooldown from cart1.jsx
                                    }
                                }
                                previousGrayData = currentGrayData;
                                URL.revokeObjectURL(url);
                            }
                        })["Cart.useEffect.fetchAndProcessImage"];
                    } catch (error) {
                        console.error("Error fetching or processing image:", error);
                    }
                }
            }["Cart.useEffect.fetchAndProcessImage"];
            const interval = setInterval(fetchAndProcessImage, 250); // Updated interval from cart1.jsx
            return ({
                "Cart.useEffect": ()=>clearInterval(interval)
            })["Cart.useEffect"];
        }
    }["Cart.useEffect"], [
        roi
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Cart.useEffect": ()=>{
            if (itemPlaced) {
                captureAndSendImage();
                setItemPlaced(false); // Reset itemPlaced after prediction
            }
        }
    }["Cart.useEffect"], [
        itemPlaced
    ]);
    const toGrayscale = (imageData)=>{
        const { data, width, height } = imageData;
        const grayData = new Uint8ClampedArray(width * height);
        for(let i = 0; i < data.length; i += 4){
            const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
            grayData[i / 4] = gray;
        }
        return grayData;
    };
    const detectMotion = (prevGray, currentGray, width, height, cellSize, threshold)=>{
        const motionCells = [];
        for(let y = 0; y <= height - cellSize; y += cellSize){
            for(let x = 0; x <= width - cellSize; x += cellSize){
                let cellDiff = 0;
                for(let i = y; i < y + cellSize; i++){
                    for(let j = x; j < x + cellSize; j++){
                        const index = i * width + j;
                        if (index < prevGray.length) {
                            cellDiff += Math.abs(prevGray[index] - currentGray[index]);
                        }
                    }
                }
                if (cellDiff > threshold) {
                    motionCells.push({
                        x,
                        y
                    });
                }
            }
        }
        return motionCells;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Cart.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                const savedItems = localStorage.getItem("cartItems");
                setCartItems(savedItems ? JSON.parse(savedItems) : []);
            }
        }
    }["Cart.useEffect"], []);
    const subtotal = cartItems.reduce((acc, item)=>{
        if (item.weight_flag === 1) {
            return acc + item.price * (item.weight || 0);
        }
        return acc + item.price * (item.quantity || 1);
    }, 0).toFixed(2);
    const totalAmount = Math.round(parseFloat(subtotal));
    const handleDropdownChange = async (index, newProductName)=>{
        try {
            const isWeightBased = WEIGHT_BASED_ITEMS.has(newProductName);
            const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
            const priceResponse = await fetch(`/api/${priceEndpoint}?productName=${encodeURIComponent(newProductName)}`);
            if (!priceResponse.ok) {
                throw new Error(`Failed to fetch price for ${newProductName}: ${priceResponse.statusText}`);
            }
            const priceData = await priceResponse.json();
            const newPrice = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
            setCartItems((prevCartItems)=>{
                const updatedCartItems = prevCartItems.map((item, i)=>{
                    if (i === index) {
                        const updatedItem = {
                            ...item,
                            selectedProduct: newProductName,
                            productName: newProductName,
                            price: newPrice,
                            weight_flag: isWeightBased ? 1 : 0
                        };
                        if (item.weight_flag !== (isWeightBased ? 1 : 0)) {
                            if (isWeightBased) {
                                delete updatedItem.quantity;
                                updatedItem.weight = 0;
                            } else {
                                delete updatedItem.weight;
                                updatedItem.quantity = 1;
                            }
                        }
                        return updatedItem;
                    }
                    return item;
                });
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
        } catch (error) {
            console.error("Error updating product price:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to update product price");
        }
    };
    // 
    // const captureAndSendImage = useCallback(async () => {
    //   try {
    //     toast.info("Capturing image...", { autoClose: 800 });
    //     const response = await fetch("http://*************:9000/image");
    //     if (!response.ok) {
    //       throw new Error(`Image capture failed: ${response.statusText}`);
    //     }
    //     const blob = await response.blob();
    //     const formData = new FormData();
    //     formData.append("image", blob, "raspberrypi.jpg");
    //     toast.info("Sending image for prediction...", { autoClose: 800 });
    //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
    //       method: "POST",
    //       body: formData,
    //     });
    //     if (!predictResponse.ok) {
    //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
    //     }
    //     const data = await predictResponse.json();
    //     console.log(data);
    //     const { yolo_tag, clip_tag, weight_flag } = data;
    //     const weightResponse = await fetch("http://*************:9000/weight");
    //     if (!weightResponse.ok) {
    //       throw new Error("Failed to fetch weight");
    //     }
    //     const weightData = await weightResponse.json();
    //     const weight = weightData.weight;
    //     setClipTag(clip_tag);
    //     console.log("Clip Tag: ", clip_tag);
    //     // Update cart items based on prediction
    //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
    //     const newCartItems = await Promise.all(
    //       tags.map(async (productName, index) => {
    //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
    //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
    //         const priceResponse = await fetch(
    //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
    //         );
    //         if (!priceResponse.ok) {
    //           throw new Error(`Failed to fetch price for ${productName}`);
    //         }
    //         const priceData = await priceResponse.json();
    //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
    //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);
    //         return {
    //           productName,
    //           weight_flag: isWeightBased ? 1 : 0,
    //           alternatives: [productName, ...alternatives],
    //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
    //         };
    //       })
    //     );
    //     // Remove items not in the predict response
    //     setCartItems((prevCartItems) => {
    //       const updatedCartItems = prevCartItems.filter((item) =>
    //         tags.includes(item.productName)
    //       );
    //       // Add new items from the predict response
    //       newCartItems.forEach((newItem) => {
    //         const existingItemIndex = updatedCartItems.findIndex(
    //           (item) => item.productName === newItem.productName
    //         );
    //         if (existingItemIndex > -1) {
    //           const existingItem = updatedCartItems[existingItemIndex];
    //           if (newItem.weight_flag === 1) {
    //             updatedCartItems[existingItemIndex] = {
    //               ...existingItem,
    //               weight: (existingItem.weight || 0) + (newItem.weight || 0),
    //             };
    //           } else {
    //             updatedCartItems[existingItemIndex] = {
    //               ...existingItem,
    //               quantity: (existingItem.quantity || 1) + (newItem.quantity || 1),
    //             };
    //           }
    //         } else {
    //           updatedCartItems.push(newItem);
    //         }
    //       });
    //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    //       return updatedCartItems;
    //     });
    //     setWeight(weight);
    //   } catch (error) {
    //     console.error("Error capturing or sending image:", error);
    //   }
    // }, []);
    // const captureAndSendImage = useCallback(async () => {
    //   try {
    //     toast.info("Capturing image...", { autoClose: 800 });
    //     const response = await fetch("http://*************:9000/image");
    //     if (!response.ok) {
    //       throw new Error(`Image capture failed: ${response.statusText}`);
    //     }
    //     const blob = await response.blob();
    //     const formData = new FormData();
    //     formData.append("image", blob, "raspberrypi.jpg");
    //     toast.info("Sending image for prediction...", { autoClose: 800 });
    //     const predictResponse = await fetch("https://shrew-golden-toucan.ngrok-free.app/predict", {
    //       method: "POST",
    //       body: formData,
    //     });
    //     if (!predictResponse.ok) {
    //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
    //     }
    //     const data = await predictResponse.json();
    //     console.log(data);
    //     const { yolo_tag, clip_tag, weight_flag } = data;
    //     const weightResponse = await fetch("http://*************:9000/weight");
    //     if (!weightResponse.ok) {
    //       throw new Error("Failed to fetch weight");
    //     }
    //     const weightData = await weightResponse.json();
    //     const weight = weightData.weight;
    //     setClipTag(clip_tag);
    //     console.log("Clip Tag: ", clip_tag);
    //     // Process predicted items
    //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];
    //     const newCartItems = await Promise.all(
    //       tags.map(async (productName, index) => {
    //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
    //         const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
    //         const priceResponse = await fetch(
    //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`
    //         );
    //         if (!priceResponse.ok) {
    //           throw new Error(`Failed to fetch price for ${productName}`);
    //         }
    //         const priceData = await priceResponse.json();
    //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
    //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);
    //         return {
    //           productName,
    //           weight_flag: isWeightBased ? 1 : 0,
    //           alternatives: [productName, ...alternatives],
    //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),
    //           fromPredict: true  // Mark items from prediction
    //         };
    //       })
    //     );
    //     // Update cart items
    //     setCartItems((prevCartItems) => {
    //       // Keep manually added items
    //       const manualItems = prevCartItems.filter(item => !item.fromPredict);
    //       // Merge predicted items with existing quantities/weights
    //       const predictedItems = newCartItems.map(newItem => {
    //         const existingItem = prevCartItems.find(
    //           item => item.productName === newItem.productName && item.fromPredict
    //         );
    //         if (existingItem) {
    //           // Preserve existing quantity/weight for previously predicted items
    //           if (newItem.weight_flag === 1) {
    //             return {
    //               ...newItem,
    //               weight: existingItem.weight
    //             };
    //           } else {
    //             return {
    //               ...newItem,
    //               quantity: existingItem.quantity
    //             };
    //           }
    //         }
    //         return newItem;
    //       });
    //       // Combine manual and predicted items
    //       const updatedCartItems = [...manualItems, ...predictedItems];
    //       localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    //       return updatedCartItems;
    //     });
    //     setWeight(weight);
    //   } catch (error) {
    //     console.error("Error capturing or sending image:", error);
    //   }
    // }, []);
    const captureAndSendImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Cart.useCallback[captureAndSendImage]": async ()=>{
            try {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Capturing image...", {
                    autoClose: 800
                });
                const response = await fetch("http://*************:9000/image");
                if (!response.ok) {
                    throw new Error(`Image capture failed: ${response.statusText}`);
                }
                const blob = await response.blob();
                const formData = new FormData();
                formData.append("image", blob, "raspberrypi.jpg");
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Sending image for prediction...", {
                    autoClose: 800
                });
                const predictResponse = await fetch("http://*************:9000/predict", {
                    method: "POST",
                    body: formData
                });
                if (!predictResponse.ok) {
                    throw new Error(`Prediction request failed: ${predictResponse.statusText}`);
                }
                const data = await predictResponse.json();
                console.log(data);
                const { yolo_tag, clip_tag, weight_flag } = data;
                const weightResponse = await fetch("http://*************:9000/weight");
                if (!weightResponse.ok) {
                    throw new Error("Failed to fetch weight");
                }
                const weightData = await weightResponse.json();
                const weight = weightData.weight;
                setClipTag(clip_tag);
                console.log("Clip Tag: ", clip_tag);
                // Process predicted items
                const tags = Array.isArray(yolo_tag) ? yolo_tag : [
                    yolo_tag
                ];
                // Calculate product counts here, after tags is defined
                const productCounts = tags.reduce({
                    "Cart.useCallback[captureAndSendImage].productCounts": (acc, product)=>{
                        acc[product] = (acc[product] || 0) + 1;
                        return acc;
                    }
                }["Cart.useCallback[captureAndSendImage].productCounts"], {});
                const newCartItems = await Promise.all(tags.map({
                    "Cart.useCallback[captureAndSendImage]": async (productName, index)=>{
                        const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
                        const priceEndpoint = isWeightBased ? "pricePerKg" : "pricePer";
                        const priceResponse = await fetch(`/api/${priceEndpoint}?productName=${encodeURIComponent(productName)}`);
                        if (!priceResponse.ok) {
                            throw new Error(`Failed to fetch price for ${productName}`);
                        }
                        const priceData = await priceResponse.json();
                        const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;
                        const alternatives = clip_tag[index].filter({
                            "Cart.useCallback[captureAndSendImage].alternatives": (tag)=>tag !== productName
                        }["Cart.useCallback[captureAndSendImage].alternatives"]);
                        return {
                            productName,
                            weight_flag: isWeightBased ? 1 : 0,
                            alternatives: [
                                productName,
                                ...alternatives
                            ],
                            ...isWeightBased ? {
                                weight: (weight || 0) * productCounts[productName],
                                price: price || 0
                            } : {
                                quantity: productCounts[productName],
                                price: price || 0
                            },
                            fromPredict: true
                        };
                    }
                }["Cart.useCallback[captureAndSendImage]"]));
                // Update cart items based on lock status or add mode
                setCartItems({
                    "Cart.useCallback[captureAndSendImage]": (prevCartItems)=>{
                        if (isCartLocked || isAddMode) {
                            // When locked or in add mode, preserve all existing items and only add truly new items
                            const updatedCartItems = [
                                ...prevCartItems
                            ];
                            newCartItems.forEach({
                                "Cart.useCallback[captureAndSendImage]": (newItem)=>{
                                    const existingItemIndex = updatedCartItems.findIndex({
                                        "Cart.useCallback[captureAndSendImage].existingItemIndex": (item)=>item.productName === newItem.productName
                                    }["Cart.useCallback[captureAndSendImage].existingItemIndex"]);
                                    if (existingItemIndex === -1) {
                                        // Only add if it's a completely new item not in cart
                                        updatedCartItems.push({
                                            ...newItem,
                                            fromPredict: true,
                                            addedInAddMode: isAddMode
                                        });
                                    } else if (isAddMode) {
                                        // In add mode, increase quantity/weight of existing items
                                        const existingItem = updatedCartItems[existingItemIndex];
                                        if (newItem.weight_flag === 1) {
                                            updatedCartItems[existingItemIndex] = {
                                                ...existingItem,
                                                weight: (existingItem.weight || 0) + (newItem.weight || 0)
                                            };
                                        } else {
                                            updatedCartItems[existingItemIndex] = {
                                                ...existingItem,
                                                quantity: (existingItem.quantity || 1) + (newItem.quantity || 1)
                                            };
                                        }
                                    }
                                // If locked and item exists, don't modify it - keep existing quantities/weights
                                }
                            }["Cart.useCallback[captureAndSendImage]"]);
                            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                            return updatedCartItems;
                        } else {
                            // When not locked, use original logic (replace predicted items)
                            const manualItems = prevCartItems.filter({
                                "Cart.useCallback[captureAndSendImage].manualItems": (item)=>!item.fromPredict
                            }["Cart.useCallback[captureAndSendImage].manualItems"]);
                            const uniquePredictedItems = Array.from(new Set(newCartItems.map({
                                "Cart.useCallback[captureAndSendImage].uniquePredictedItems": (item)=>item.productName
                            }["Cart.useCallback[captureAndSendImage].uniquePredictedItems"]))).map({
                                "Cart.useCallback[captureAndSendImage].uniquePredictedItems": (productName)=>{
                                    const item = newCartItems.find({
                                        "Cart.useCallback[captureAndSendImage].uniquePredictedItems.item": (item)=>item.productName === productName
                                    }["Cart.useCallback[captureAndSendImage].uniquePredictedItems.item"]);
                                    const existingItem = prevCartItems.find({
                                        "Cart.useCallback[captureAndSendImage].uniquePredictedItems.existingItem": (prevItem)=>prevItem.productName === productName && prevItem.fromPredict
                                    }["Cart.useCallback[captureAndSendImage].uniquePredictedItems.existingItem"]);
                                    if (existingItem) {
                                        if (item.weight_flag === 1) {
                                            return {
                                                ...item,
                                                weight: existingItem.weight
                                            };
                                        } else {
                                            return {
                                                ...item,
                                                quantity: existingItem.quantity
                                            };
                                        }
                                    }
                                    return item;
                                }
                            }["Cart.useCallback[captureAndSendImage].uniquePredictedItems"]);
                            const updatedCartItems = [
                                ...manualItems,
                                ...uniquePredictedItems
                            ];
                            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                            return updatedCartItems;
                        }
                    }
                }["Cart.useCallback[captureAndSendImage]"]);
                setWeight(weight);
            } catch (error) {
                console.error("Error capturing or sending image:", error);
            }
        }
    }["Cart.useCallback[captureAndSendImage]"], [
        isCartLocked,
        isAddMode
    ]);
    // Modified addProduct function
    const addProduct = (product)=>{
        setCartItems((prevCartItems)=>{
            const updatedCartItems = [
                ...prevCartItems
            ];
            const existingItemIndex = updatedCartItems.findIndex((item)=>item.productName === product.productName && !item.fromPredict);
            if (existingItemIndex === -1) {
                // Add new item without fromPredict flag
                updatedCartItems.push({
                    ...product,
                    fromPredict: false
                });
            } else {
                // Update existing manual item
                const existingItem = updatedCartItems[existingItemIndex];
                if (product.weight_flag === 1) {
                    updatedCartItems[existingItemIndex] = {
                        ...existingItem,
                        weight: (existingItem.weight || 0) + (product.weight || 0)
                    };
                } else {
                    updatedCartItems[existingItemIndex] = {
                        ...existingItem,
                        quantity: (existingItem.quantity || 1) + (product.quantity || 1)
                    };
                }
            }
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleIncreaseQuantity = (productName)=>{
        const cartItems = JSON.parse(localStorage.getItem("cartItems")) || [];
        const updatedCartItems = cartItems.map((item)=>item.productName === productName ? {
                ...item,
                quantity: (item.quantity || 1) + 1
            } : item);
        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
        setCartItems(updatedCartItems);
    };
    const handleDecreaseQuantity = (productName)=>{
        setCartItems((prevItems)=>{
            const updatedCartItems = prevItems.map((item)=>{
                if (item.productName === productName) {
                    const newQuantity = (item.quantity || 1) - 1;
                    return newQuantity > 0 ? {
                        ...item,
                        quantity: newQuantity
                    } : null;
                }
                return item;
            }).filter(Boolean);
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleRemoveTag = (tagToRemove)=>{
        setCartItems((prevCartItems)=>{
            const indexToRemove = prevCartItems.findIndex((item)=>item.productName === tagToRemove);
            if (indexToRemove === -1) return prevCartItems;
            const itemToRemove = prevCartItems[indexToRemove];
            // If cart is locked and item is locked, don't remove
            if (isCartLocked && itemToRemove.isLocked) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warning("Cannot remove locked item. Unlock cart first.");
                return prevCartItems;
            }
            const updatedCartItems = [
                ...prevCartItems.slice(0, indexToRemove),
                ...prevCartItems.slice(indexToRemove + 1)
            ];
            localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
            return updatedCartItems;
        });
    };
    const handleAddToCart = async (item)=>{
        // Ensure productName is set (for search results)
        const productName = item.productName || item.name || "";
        if (!productName) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Product name is missing.");
            return;
        }
        const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);
        let currentWeight = 0;
        if (isWeightBased) {
            try {
                const response = await fetch("http://************:9000/weight");
                if (response.ok) {
                    const data = await response.json();
                    currentWeight = data.weight || 0;
                }
            } catch (error) {
                console.error("Error fetching weight:", error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to get weight from scale");
                return;
            }
        }
        let updatedCartItems;
        if (isWeightBased) {
            updatedCartItems = [
                ...cartItems,
                {
                    ...item,
                    productName,
                    weight_flag: 1,
                    alternatives: [
                        productName
                    ],
                    weight: currentWeight,
                    price: item.pricePerKg || item.price || 0
                }
            ];
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(`Added ${currentWeight}KG of ${productName} to cart`);
        } else {
            const existingItemIndex = cartItems.findIndex((cartItem)=>cartItem.productName === productName && !cartItem.weight_flag);
            if (existingItemIndex > -1) {
                updatedCartItems = cartItems.map((cartItem, index)=>{
                    if (index === existingItemIndex) {
                        const newQuantity = (cartItem.quantity || 1) + 1;
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(`Updated ${productName} quantity to ${newQuantity}`);
                        return {
                            ...cartItem,
                            quantity: newQuantity,
                            price: item.price || 0
                        };
                    }
                    return cartItem;
                });
            } else {
                updatedCartItems = [
                    ...cartItems,
                    {
                        ...item,
                        productName,
                        weight_flag: 0,
                        alternatives: [
                            productName
                        ],
                        quantity: 1,
                        price: item.price || 0
                    }
                ];
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(`Added ${productName} to cart`);
            }
        }
        setCartItems(updatedCartItems);
        localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
    };
    const handleOpenAddProductDialog = ()=>{
        setOpenAddProductDialog(true);
    };
    const handleCloseAddProductDialog = ()=>{
        setOpenAddProductDialog(false);
        setNewProductName("");
        setNewProductPrice("");
        setNewProductQuantity(1);
        setNewProductShortcode("");
    };
    const handleAddProductSubmit = ()=>{
        if (!newProductName || !newProductPrice) {
            setError("Please enter a product name and price.");
            return;
        }
        const newProduct = {
            _id: Date.now().toString(),
            productName: newProductName,
            price: parseFloat(newProductPrice),
            quantity: newProductQuantity,
            shortcode: newProductShortcode
        };
        setItems([
            ...items,
            newProduct
        ]);
        handleAddToCart(newProduct);
        handleCloseAddProductDialog();
    };
    // Modified scan logic
    const handleClick = ()=>{
        captureAndSendImage();
        setTimeout(()=>setClicked(false), 600);
    };
    const handleClearCart = ()=>{
        setCartItems([]); // Clear cart items state
        localStorage.removeItem("cartItems"); // Remove from localStorage
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Cart cleared successfully!"); // Optional success message
    };
    const handleToggleLock = ()=>{
        const newLockState = !isCartLocked;
        setIsCartLocked(newLockState);
        if (newLockState) {
            // When locking, mark all current items as locked and disable add mode
            setIsAddMode(false);
            setCartItems((prevCartItems)=>{
                const updatedCartItems = prevCartItems.map((item)=>({
                        ...item,
                        isLocked: true,
                        lockedAt: Date.now()
                    }));
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Cart locked! Items will not be removed when taken from tray.");
        } else {
            // When unlocking, remove lock markers
            setCartItems((prevCartItems)=>{
                const updatedCartItems = prevCartItems.map((item)=>{
                    const { isLocked, lockedAt, ...itemWithoutLock } = item;
                    return itemWithoutLock;
                });
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Cart unlocked! Items will be updated based on tray contents.");
        }
    };
    const handleToggleAddMode = ()=>{
        const newAddMode = !isAddMode;
        setIsAddMode(newAddMode);
        if (newAddMode) {
            // When enabling add mode, disable lock mode
            setIsCartLocked(false);
            setCartItems((prevCartItems)=>{
                const updatedCartItems = prevCartItems.map((item)=>{
                    const { isLocked, lockedAt, ...itemWithoutLock } = item;
                    return itemWithoutLock;
                });
                localStorage.setItem("cartItems", JSON.stringify(updatedCartItems));
                return updatedCartItems;
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Add Mode enabled! New items will be added to cart without removing existing ones.");
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Add Mode disabled! Cart will update based on tray contents.");
        }
    };
    const fetchSearchResults = async (query)=>{
        try {
            const response = await fetch(`/api/search?query=${encodeURIComponent(query)}`);
            if (!response.ok) throw new Error("Failed to fetch search results");
            const data = await response.json();
            const formattedResults = data.map((item)=>({
                    name: item.productName,
                    price: item.price
                }));
            setDisplayedItems(formattedResults);
        } catch (error) {
            console.error("Error fetching search results:", error);
        }
    };
    const handleSearch = (query)=>{
        setSearchQuery(query);
        if (query.trim() === "") {
            setDisplayedItems([]);
            return;
        }
        fetchSearchResults(query);
    };
    const handleInputChange = (e)=>{
        const query = e.target.value;
        setSearchQuery(query);
        handleSearch(query);
    };
    const handleWeightChange = (productName, newWeight)=>{
        setCartItems((prevItems)=>{
            const updatedItems = prevItems.map((item)=>{
                if (item.productName === productName) {
                    const weight = Math.max(0, Math.round(parseFloat(newWeight || 0) * 1000) / 1000);
                    return {
                        ...item,
                        weight: weight
                    };
                }
                return item;
            });
            localStorage.setItem("cartItems", JSON.stringify(updatedItems));
            return updatedItems;
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "jsx-930577d8485a3e90" + " " + "w-full max-w-full mx-auto px-4 py-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "jsx-930577d8485a3e90" + " " + "grid grid-cols-1 lg:grid-cols-5 gap-6 h-[calc(100vh-48px)]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-930577d8485a3e90" + " " + "lg:col-span-3 bg-white rounded-2xl shadow-xl p-6 overflow-hidden flex flex-col min-h-[600px]",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "flex items-center justify-between mb-7",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "jsx-930577d8485a3e90" + " " + "text-2xl font-bold text-gray-800 flex items-center",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faShoppingCart"],
                                                    className: "mr-3 text-blue-600 text-3xl"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1144,
                                                    columnNumber: 17
                                                }, this),
                                                "Your Cart ",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-930577d8485a3e90" + " " + "ml-2 text-xl font-semibold text-blue-700",
                                                    children: [
                                                        "(",
                                                        cartItems.length,
                                                        " items)"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1145,
                                                    columnNumber: 27
                                                }, this),
                                                isCartLocked && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-930577d8485a3e90" + " " + "ml-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full flex items-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            style: {
                                                                fontSize: 16,
                                                                marginRight: 4
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1148,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Locked"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1147,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1143,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-930577d8485a3e90" + " " + "flex gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: ()=>setOpenSearchDialog(true),
                                                    color: "primary",
                                                    size: "large",
                                                    sx: {
                                                        width: 48,
                                                        height: 48,
                                                        borderRadius: 2,
                                                        border: '1px solid #1976d2',
                                                        background: '#f5faff',
                                                        mr: 1
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            fontSize: 28
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1160,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1154,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: ()=>setOpenAddProductDialog(true),
                                                    color: "primary",
                                                    size: "large",
                                                    sx: {
                                                        width: 48,
                                                        height: 48,
                                                        borderRadius: 2,
                                                        border: '1px solid #1976d2',
                                                        background: '#f5faff',
                                                        mr: 1
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$AddOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            fontSize: 28
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1168,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1162,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: handleToggleLock,
                                                    color: isCartLocked ? "warning" : "default",
                                                    size: "large",
                                                    sx: {
                                                        width: 48,
                                                        height: 48,
                                                        borderRadius: 2,
                                                        border: isCartLocked ? '1px solid #ff9800' : '1px solid #9e9e9e',
                                                        background: isCartLocked ? '#fff8e1' : '#f5f5f5',
                                                        mr: 1
                                                    },
                                                    title: isCartLocked ? "Cart is locked - click to unlock" : "Cart is unlocked - click to lock",
                                                    children: isCartLocked ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            fontSize: 28
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1184,
                                                        columnNumber: 35
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$LockOpen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            fontSize: 28
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1184,
                                                        columnNumber: 75
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1170,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    onClick: handleClearCart,
                                                    color: "error",
                                                    size: "large",
                                                    sx: {
                                                        width: 48,
                                                        height: 48,
                                                        borderRadius: 2,
                                                        border: '1px solid #e57373',
                                                        background: '#fff5f5'
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        style: {
                                                            fontSize: 28
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1192,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1186,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1153,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1142,
                                    columnNumber: 13
                                }, this),
                                cartItems.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "flex-1 flex flex-col items-center justify-center text-gray-500 py-10",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faShoppingCart"],
                                            className: "text-[60px] mb-5 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1199,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "jsx-930577d8485a3e90" + " " + "text-xl font-bold mb-2",
                                            children: "Your cart is empty"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1200,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "jsx-930577d8485a3e90" + " " + "text-lg text-center mb-4",
                                            children: "Add some items to get started!"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1201,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                            onClick: ()=>setOpenSearchDialog(true),
                                            variant: "contained",
                                            sx: {
                                                fontSize: 18,
                                                px: 3,
                                                py: 2,
                                                borderRadius: 2
                                            },
                                            startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                style: {
                                                    fontSize: 24
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1206,
                                                columnNumber: 30
                                            }, void 0),
                                            children: "Browse Products"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1202,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1198,
                                    columnNumber: 15
                                }, this),
                                cartItems.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "flex-1 overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-930577d8485a3e90" + " " + "h-full overflow-y-auto pr-2 space-y-5 custom-scrollbar",
                                        children: cartItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                style: {
                                                    minHeight: 90
                                                },
                                                className: "jsx-930577d8485a3e90" + " " + "bg-gray-50 rounded-2xl p-6 border border-gray-300 hover:shadow-xl transition-shadow min-h-[90px] flex flex-col justify-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-930577d8485a3e90" + " " + "flex items-start justify-between mb-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "jsx-930577d8485a3e90" + " " + "flex-1 min-w-0 flex items-center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$FormControl$2f$FormControl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FormControl$3e$__["FormControl"], {
                                                                        size: "medium",
                                                                        className: "w-full max-w-sm",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Select$2f$Select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Select$3e$__["Select"], {
                                                                            value: item.selectedProduct || item.productName || "",
                                                                            onChange: (e)=>handleDropdownChange(index, e.target.value),
                                                                            displayEmpty: true,
                                                                            className: "text-lg",
                                                                            sx: {
                                                                                fontSize: 18,
                                                                                minHeight: 40
                                                                            },
                                                                            children: item.alternatives && item.alternatives.map((alternative, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$MenuItem$2f$MenuItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MenuItem$3e$__["MenuItem"], {
                                                                                    value: alternative,
                                                                                    style: {
                                                                                        fontSize: 18,
                                                                                        minHeight: 32
                                                                                    },
                                                                                    children: alternative
                                                                                }, i, false, {
                                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                                    lineNumber: 1237,
                                                                                    columnNumber: 35
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1226,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1225,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    isCartLocked && item.isLocked && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "jsx-930577d8485a3e90" + " " + "ml-2 px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded flex items-center",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    marginRight: 2
                                                                                }
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                                lineNumber: 1245,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            "Locked"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1244,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    isCartLocked && !item.isLocked && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "jsx-930577d8485a3e90" + " " + "ml-2 px-2 py-1 bg-green-100 text-green-600 text-xs font-medium rounded flex items-center",
                                                                        children: "New"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1250,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1224,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                onClick: ()=>handleRemoveTag(item.productName),
                                                                size: "medium",
                                                                className: "text-red-500 hover:bg-red-50 ml-2",
                                                                sx: {
                                                                    width: 38,
                                                                    height: 38
                                                                },
                                                                disabled: isCartLocked && item.isLocked,
                                                                title: isCartLocked && item.isLocked ? "Cannot remove locked item" : "Remove item",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    style: {
                                                                        fontSize: 22
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1263,
                                                                    columnNumber: 27
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1255,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1223,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-930577d8485a3e90" + " " + "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "jsx-930577d8485a3e90" + " " + "flex items-center space-x-5",
                                                                children: item.weight_flag === 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-930577d8485a3e90" + " " + "flex items-center space-x-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "jsx-930577d8485a3e90" + " " + "text-lg text-gray-700 font-semibold",
                                                                            children: "Weight:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1271,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                                                            type: "number",
                                                                            value: parseFloat(item.weight || 0),
                                                                            onChange: (e)=>handleWeightChange(item.productName, e.target.value),
                                                                            size: "medium",
                                                                            className: "w-24 text-lg",
                                                                            inputProps: {
                                                                                style: {
                                                                                    fontSize: 16,
                                                                                    height: 32
                                                                                }
                                                                            },
                                                                            slotProps: {
                                                                                input: {
                                                                                    endAdornment: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: "jsx-930577d8485a3e90" + " " + "text-lg text-gray-500",
                                                                                        children: "KG"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1287,
                                                                                        columnNumber: 39
                                                                                    }, void 0),
                                                                                    inputProps: {
                                                                                        min: 0,
                                                                                        step: 0.001
                                                                                    }
                                                                                }
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1272,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1270,
                                                                    columnNumber: 29
                                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-930577d8485a3e90" + " " + "flex items-center space-x-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "jsx-930577d8485a3e90" + " " + "text-lg text-gray-700 font-semibold",
                                                                            children: "Qty:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1296,
                                                                            columnNumber: 31
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "jsx-930577d8485a3e90" + " " + "flex items-center space-x-2 bg-white rounded-xl border px-2 py-1",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                    onClick: ()=>handleDecreaseQuantity(item.productName),
                                                                                    size: "medium",
                                                                                    className: "text-red-500 hover:bg-red-50",
                                                                                    sx: {
                                                                                        width: 28,
                                                                                        height: 28
                                                                                    },
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                        style: {
                                                                                            fontSize: 18
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1306,
                                                                                        columnNumber: 35
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                                    lineNumber: 1298,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "jsx-930577d8485a3e90" + " " + "px-3 py-1 text-lg font-bold",
                                                                                    children: item.quantity || 1
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                                    lineNumber: 1308,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                    onClick: ()=>handleIncreaseQuantity(item.productName),
                                                                                    size: "medium",
                                                                                    className: "text-green-500 hover:bg-green-50",
                                                                                    sx: {
                                                                                        width: 28,
                                                                                        height: 28
                                                                                    },
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Add$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                        style: {
                                                                                            fontSize: 18
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                                        lineNumber: 1319,
                                                                                        columnNumber: 35
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                                    lineNumber: 1311,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                                            lineNumber: 1297,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1295,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1268,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "jsx-930577d8485a3e90" + " " + "text-right",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "jsx-930577d8485a3e90" + " " + "text-lg text-gray-700 font-semibold",
                                                                        children: [
                                                                            "₹",
                                                                            item.price,
                                                                            " ",
                                                                            item.weight_flag === 1 ? '/kg' : '/item'
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1326,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "jsx-930577d8485a3e90" + " " + "text-xl font-bold text-gray-900 mt-1",
                                                                        children: [
                                                                            "₹",
                                                                            (item.price * (item.weight_flag === 1 ? item.weight : item.quantity)).toFixed(2)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                                        lineNumber: 1329,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1325,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1267,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1218,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1216,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1215,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "mt-6 pt-4 border-t border-gray-300",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-930577d8485a3e90" + " " + "flex items-center justify-between mb-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: "jsx-930577d8485a3e90" + " " + "text-lg font-semibold text-gray-800 flex items-center",
                                                    children: [
                                                        "Order",
                                                        isCartLocked && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-930577d8485a3e90" + " " + "ml-2 px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        marginRight: 2
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                                    lineNumber: 1353,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Locked"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1352,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1349,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-930577d8485a3e90" + " " + "flex gap-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                        onClick: handleClick,
                                                        variant: "contained",
                                                        color: "primary",
                                                        sx: {
                                                            fontSize: 14,
                                                            px: 1.5,
                                                            py: 1,
                                                            borderRadius: 2,
                                                            minWidth: 70
                                                        },
                                                        startIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$react$2d$fontawesome$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FontAwesomeIcon"], {
                                                            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fortawesome$2f$free$2d$solid$2d$svg$2d$icons$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["faShoppingCart"],
                                                            style: {
                                                                fontSize: 16
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1364,
                                                            columnNumber: 32
                                                        }, void 0),
                                                        children: "Scan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1359,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1358,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1348,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-930577d8485a3e90" + " " + "space-y-2 mb-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-930577d8485a3e90" + " " + "flex justify-between text-base",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-930577d8485a3e90" + " " + "text-gray-600 font-semibold",
                                                            children: "Items:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1373,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-930577d8485a3e90" + " " + "font-bold",
                                                            children: cartItems.length
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1374,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1372,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-930577d8485a3e90" + " " + "flex justify-between text-base",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-930577d8485a3e90" + " " + "text-gray-600 font-semibold",
                                                            children: "Subtotal:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1377,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-930577d8485a3e90" + " " + "font-bold",
                                                            children: [
                                                                "₹",
                                                                subtotal
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1378,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1376,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-930577d8485a3e90" + " " + "border-t pt-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-930577d8485a3e90" + " " + "flex justify-between text-lg font-bold",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-930577d8485a3e90",
                                                                children: "Total:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1382,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-930577d8485a3e90" + " " + "text-blue-600",
                                                                children: Number(totalAmount).toLocaleString("en-IN", {
                                                                    style: "currency",
                                                                    currency: "INR",
                                                                    minimumFractionDigits: 0,
                                                                    maximumFractionDigits: 3
                                                                })
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                                lineNumber: 1383,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/cart/cart.jsx",
                                                        lineNumber: 1381,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1380,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1371,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1346,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1141,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-930577d8485a3e90" + " " + "lg:col-span-2 flex flex-col gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "bg-white rounded-2xl shadow-xl p-4 sm:p-6 flex-1 min-h-[300px] sm:min-h-[400px] lg:min-h-[500px]",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$ads$2d$section$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1403,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1402,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-930577d8485a3e90" + " " + "bg-white rounded-2xl shadow-xl p-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-930577d8485a3e90" + " " + "flex gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                variant: "contained",
                                                color: "success",
                                                size: "medium",
                                                className: "flex-1",
                                                sx: {
                                                    fontSize: 14,
                                                    py: 1.5,
                                                    borderRadius: 2,
                                                    fontWeight: 'bold'
                                                },
                                                disabled: cartItems.length === 0,
                                                children: "Checkout"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1409,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                                onClick: handleClearCart,
                                                variant: "outlined",
                                                color: "error",
                                                size: "medium",
                                                className: "flex-1",
                                                sx: {
                                                    fontSize: 14,
                                                    py: 1.5,
                                                    borderRadius: 2,
                                                    fontWeight: 'bold'
                                                },
                                                disabled: cartItems.length === 0,
                                                children: "Clear Cart"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1419,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1408,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1407,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1400,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1139,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__["Dialog"], {
                    open: openSearchDialog,
                    onClose: ()=>setOpenSearchDialog(false),
                    fullWidth: true,
                    maxWidth: "sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__["DialogTitle"], {
                            children: [
                                "Search Products",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    edge: "end",
                                    color: "inherit",
                                    onClick: ()=>setOpenSearchDialog(false),
                                    "aria-label": "close",
                                    sx: {
                                        position: "absolute",
                                        right: 10,
                                        top: 10
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                        fileName: "[project]/src/components/cart/cart.jsx",
                                        lineNumber: 1451,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1444,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1442,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__["DialogContent"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                    fullWidth: true,
                                    variant: "outlined",
                                    placeholder: "Search for a product...",
                                    value: searchQuery,
                                    onChange: handleInputChange,
                                    sx: {
                                        mb: 2,
                                        fontSize: 15,
                                        pr: 0
                                    },
                                    InputProps: {
                                        endAdornment: searchQuery && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            "aria-label": "clear search",
                                            size: "small",
                                            onClick: ()=>setSearchQuery(""),
                                            edge: "end",
                                            sx: {
                                                mr: 0.5
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$Delete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                fontSize: "small"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/cart/cart.jsx",
                                                lineNumber: 1472,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1465,
                                            columnNumber: 21
                                        }, void 0)
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1455,
                                    columnNumber: 13
                                }, this),
                                displayedItems.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$List$2f$List$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__["List"], {
                                    children: displayedItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$ListItem$2f$ListItem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListItem$3e$__["ListItem"], {
                                            button: true,
                                            onClick: ()=>{
                                                handleAddToCart(item);
                                                setOpenSearchDialog(false);
                                            },
                                            sx: {
                                                display: "flex",
                                                justifyContent: "space-between",
                                                borderBottom: "1px solid #ddd",
                                                padding: "8px"
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                                    children: item.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1495,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                                    sx: {
                                                        fontWeight: "semibold",
                                                        color: "#000000",
                                                        mt: 2
                                                    },
                                                    children: [
                                                        "₹",
                                                        item.price
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/cart/cart.jsx",
                                                    lineNumber: 1496,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1481,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1479,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Typography$2f$Typography$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Typography$3e$__["Typography"], {
                                    sx: {
                                        textAlign: "center",
                                        mt: 2,
                                        color: "gray"
                                    },
                                    children: "No products found."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1509,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1454,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1436,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Dialog$2f$Dialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Dialog$3e$__["Dialog"], {
                    open: openAddProductDialog,
                    onClose: handleCloseAddProductDialog,
                    fullWidth: true,
                    maxWidth: "sm",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogTitle$2f$DialogTitle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogTitle$3e$__["DialogTitle"], {
                            children: "Add a Product"
                        }, void 0, false, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1524,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogContent$2f$DialogContent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogContent$3e$__["DialogContent"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Autocomplete$2f$Autocomplete$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__Autocomplete$3e$__["Autocomplete"], {
                                    freeSolo: true,
                                    id: "productName-autocomplete",
                                    options: productSuggestions,
                                    getOptionLabel: (option)=>option.productName || option.name || option || "",
                                    loading: productLoading,
                                    value: newProductName,
                                    inputValue: newProductName,
                                    onInputChange: handleProductNameInput,
                                    onChange: handleProductSuggestionSelect,
                                    renderInput: (params)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                            ...params,
                                            label: "Product Name",
                                            variant: "outlined",
                                            sx: {
                                                mt: 2,
                                                width: "100%"
                                            },
                                            required: true,
                                            InputProps: {
                                                ...params.InputProps,
                                                endAdornment: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        productLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$CircularProgress$2f$CircularProgress$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CircularProgress$3e$__["CircularProgress"], {
                                                            color: "inherit",
                                                            size: 18
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/cart/cart.jsx",
                                                            lineNumber: 1547,
                                                            columnNumber: 43
                                                        }, void 0) : null,
                                                        params.InputProps.endAdornment
                                                    ]
                                                }, void 0, true)
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/cart/cart.jsx",
                                            lineNumber: 1537,
                                            columnNumber: 17
                                        }, void 0)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1526,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$TextField$2f$TextField$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TextField$3e$__["TextField"], {
                                    id: "productPrice",
                                    variant: "outlined",
                                    label: "Product Price",
                                    value: newProductPrice,
                                    onChange: (e)=>setNewProductPrice(e.target.value),
                                    sx: {
                                        mt: 2,
                                        width: "100%"
                                    },
                                    required: true
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1555,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1525,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$DialogActions$2f$DialogActions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DialogActions$3e$__["DialogActions"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    onClick: handleCloseAddProductDialog,
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1566,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$Button$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Button$3e$__["Button"], {
                                    onClick: handleAddProductSubmit,
                                    color: "primary",
                                    variant: "contained",
                                    children: "Add Product"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/cart/cart.jsx",
                                    lineNumber: 1567,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/cart/cart.jsx",
                            lineNumber: 1565,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1518,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                    ref: canvasRef,
                    style: {
                        display: "none"
                    },
                    className: "jsx-930577d8485a3e90"
                }, void 0, false, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1578,
                    columnNumber: 9
                }, this),
                showMotionToast && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "jsx-930577d8485a3e90" + " " + "toast show -mt-10",
                    children: "Motion Detected!"
                }, void 0, false, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1581,
                    columnNumber: 29
                }, this),
                showItemToast && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "jsx-930577d8485a3e90" + " " + "toast show",
                    children: "Item placed"
                }, void 0, false, {
                    fileName: "[project]/src/components/cart/cart.jsx",
                    lineNumber: 1582,
                    columnNumber: 27
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    id: "930577d8485a3e90",
                    children: ".toast.jsx-930577d8485a3e90{color:#fff;opacity:0;z-index:1000;background-color:#333;border-radius:5px;padding:10px 20px;font-size:14px;transition:all .3s;position:fixed;top:20px;right:20px;transform:translateY(-20px)}.toast.show.jsx-930577d8485a3e90{opacity:1;transform:translateY(0)}"
                }, void 0, false, void 0, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/cart/cart.jsx",
            lineNumber: 1138,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/cart/cart.jsx",
        lineNumber: 1137,
        columnNumber: 5
    }, this);
};
_s(Cart, "u8iddqT8ofzZ17MV2AsSXGt3YpM=");
_c = Cart;
const __TURBOPACK__default__export__ = Cart;
var _c;
__turbopack_refresh__.register(_c, "Cart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/overlay/welcome-overlay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__ = __turbopack_import__("[project]/node_modules/@mui/material/IconButton/IconButton.js [app-client] (ecmascript) <export default as IconButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ArrowUpward$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@mui/icons-material/esm/ArrowUpward.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
const WelcomeOverlay = ({ onProceedToCart })=>{
    _s();
    const [isAnimating, setIsAnimating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleArrowClick = ()=>{
        setIsAnimating(true);
        // Wait for animation to complete before calling onProceedToCart
        setTimeout(()=>{
            onProceedToCart();
        }, 800); // Animation duration
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `h-screen w-full relative overflow-hidden transition-transform duration-700 ease-in-out ${isAnimating ? 'transform -translate-y-full' : 'transform translate-y-0'}`,
        style: {
            backgroundImage: `url('/synecxai.png')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "absolute bottom-10 left-1/2 transform -translate-x-1/2 z-20",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$material$2f$IconButton$2f$IconButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__IconButton$3e$__["IconButton"], {
                onClick: handleArrowClick,
                sx: {
                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                    color: "#3B82F6",
                    padding: "1.5rem",
                    "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 1)",
                        transform: "scale(1.1)"
                    },
                    transition: "all 0.3s ease-in-out",
                    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
                    border: "2px solid rgba(59, 130, 246, 0.3)"
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$mui$2f$icons$2d$material$2f$esm$2f$ArrowUpward$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    sx: {
                        fontSize: "2.5rem"
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/overlay/welcome-overlay.tsx",
                    lineNumber: 51,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/overlay/welcome-overlay.tsx",
                lineNumber: 36,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/overlay/welcome-overlay.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/overlay/welcome-overlay.tsx",
        lineNumber: 23,
        columnNumber: 5
    }, this);
};
_s(WelcomeOverlay, "fVzM11GJBSX9GuOWSLFqeCknr64=");
_c = WelcomeOverlay;
const __TURBOPACK__default__export__ = WelcomeOverlay;
var _c;
__turbopack_refresh__.register(_c, "WelcomeOverlay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$cart$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/cart/cart.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$overlay$2f$welcome$2d$overlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/overlay/welcome-overlay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
function Home() {
    _s();
    const [totalPrice, setTotalPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [showCart, setShowCart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleUpdateTotalPrice = (price)=>{
        setTotalPrice(price);
    };
    const handleProceedToCart = ()=>{
        setShowCart(true);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            !showCart ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$overlay$2f$welcome$2d$overlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onProceedToCart: handleProceedToCart
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 24,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$cart$2f$cart$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                onUpdateTotalPrice: handleUpdateTotalPrice
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 26,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ToastContainer"], {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                newestOnTop: false,
                closeOnClick: true,
                rtl: false,
                pauseOnFocusLoss: true,
                draggable: true,
                pauseOnHover: true,
                theme: "light"
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
}
_s(Home, "QZI3bcPXbZbnHgs3sZjKBBiXc80=");
_c = Home;
var _c;
__turbopack_refresh__.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_f54f6f._.js.map