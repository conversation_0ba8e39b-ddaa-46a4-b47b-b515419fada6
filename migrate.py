from pymongo import MongoClient
import psycopg2
import json

# MongoDB connection
mongo_client = MongoClient("mongodb+srv://sakthivelraj:<EMAIL>/ganesh-sweets?retryWrites=true&w=majority")
mongo_db = mongo_client["ganesh-sweets"]  # your db name



# PostgreSQL connection
pg_conn = psycopg2.connect(
    host="localhost",
    database="ganesh-sweets",
    user="postgres",
    password="Venkat@2005"
)
pg_cursor = pg_conn.cursor()
# ----------------------------
def get_all_fields_and_types(collection):
    """Scan all documents to find all fields and their most accurate types."""
    field_type_map = {}

    for doc in collection.find():
        for key, value in doc.items():
            if value is None:
                continue

            current_type = type(value)
            existing_type = field_type_map.get(key)

            # Prefer float over int if mixed
            if existing_type == float:
                continue
            elif existing_type == int and current_type == float:
                field_type_map[key] = float
            elif existing_type is None:
                field_type_map[key] = current_type
            elif existing_type == str and current_type != str:
                field_type_map[key] = current_type

    return field_type_map

def map_type(py_type, value):
    """Map Python/MongoDB types to PostgreSQL types."""
    if py_type == int:
        return "BIGINT" if abs(value) > 2147483647 else "INTEGER"
    elif py_type == float:
        return "FLOAT"
    elif py_type in [list, dict]:
        return "JSONB"
    else:
        return "TEXT"

def create_pg_table(collection_name, collection):
    """Create PostgreSQL table for a MongoDB collection."""
    field_types = get_all_fields_and_types(collection)
    fields_sql = []

    for field, py_type in field_types.items():
        sample_value = collection.find_one({field: {"$exists": True}}).get(field)

        if field == "_id":
            pg_type = "TEXT PRIMARY KEY"
        else:
            pg_type = map_type(py_type, sample_value)

        fields_sql.append(f'"{field}" {pg_type}')

    schema = ", ".join(fields_sql)
    pg_cursor.execute(f'DROP TABLE IF EXISTS "{collection_name}";')
    pg_cursor.execute(f'CREATE TABLE "{collection_name}" ({schema});')

def insert_into_pg(collection_name, doc):
    """Insert a MongoDB document into PostgreSQL."""
    doc["_id"] = str(doc["_id"])  # ObjectId → string
    keys = list(doc.keys())
    values = []

    for val in doc.values():
        if isinstance(val, (dict, list)):
            values.append(json.dumps(val))
        elif isinstance(val, (int, float)):
            values.append(val)
        else:
            values.append(str(val) if val is not None else None)

    placeholders = ", ".join(["%s"] * len(keys))
    columns = ", ".join([f'"{k}"' for k in keys])
    query = f'INSERT INTO "{collection_name}" ({columns}) VALUES ({placeholders});'

    pg_cursor.execute(query, values)

# ----------------------------
# Main migration process
for collection_name in mongo_db.list_collection_names():
    collection = mongo_db[collection_name]
    print(f"Processing collection: {collection_name}")

    if collection.estimated_document_count() == 0:
        print(f"Skipping empty collection: {collection_name}")
        continue

    create_pg_table(collection_name, collection)

    for doc in collection.find():
        insert_into_pg(collection_name, doc)

pg_conn.commit()
pg_cursor.close()
pg_conn.close()
print("✅ Migration complete!")