const router = require('express').Router();
const Product = require('../models/MenuItem1');
router.get('/', async (req, res) => {
  try {
    const products = await Product.find();
    res.status(200).json(products);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching products', error: err.message });
  }
});

router.delete('/:id', async (req, res) => {
  const { id } = req.params;

  try {
    const deletedProduct = await Product.findByIdAndDelete(id);

    if (!deletedProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    res.status(200).json({ message: 'Product deleted successfully', product: deletedProduct });
  } catch (err) {
    res.status(500).json({ message: 'Error deleting product', error: err.message });
  }
});
router.patch('/:id/increase', async (req, res) => {
  const { id } = req.params;

  try {
    const product = await Product.findById(id);

    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }

    product.quantity += 1;
    await product.save();

    res.status(200).json({ message: 'Product quantity increased', product });
  } catch (err) {
    res.status(500).json({ message: 'Error increasing product quantity', error: err.message });
  }
});

module.exports = router;
