"use client";
import React, { useState } from "react";
import  Cart  from "@/components/cart/cart"
import CheckoutAd from "../ads/checkout-ad";

const Final: React.FC = () => {
  const [totalPrice, setTotalPrice] = useState<number>(0);

  const handleTotalPriceUpdate = (price: number) => {
    setTotalPrice(price);
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 p-4 h-screen overflow-hidden">
      <div className="flex-1 md:basis-2/5 h-full">
      <Cart onUpdateTotalPrice={handleTotalPriceUpdate} />


      </div>
      <div className="flex-1 md:basis-3/4 h-full">
        <CheckoutAd totalPrice={totalPrice} />
      </div>
    </div>
  );
};

export default Final;
