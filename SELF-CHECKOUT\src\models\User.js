import mongoose from 'mongoose';

const UserSchema = new mongoose.Schema({
  username: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  mobileNumber: { type: String, required: true },
  role: { type: String, required: true },
  storename: { type: String, required: true }
}, {
  timestamps: true,
});

export default mongoose.models.User || mongoose.model('User', UserSchema);
