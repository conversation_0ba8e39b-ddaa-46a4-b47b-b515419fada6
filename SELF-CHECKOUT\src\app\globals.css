@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Toast animation styles */
.toast {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Smooth transitions for cart items */
.cart-item-transition {
  transition: all 0.2s ease-in-out;
}

.cart-item-transition:hover {
  transform: translateY(-1px);
}

/* Auto-scrolling ads animation */
.auto-scroll-ads {
  animation: autoScrollLarge 45s linear infinite;
}

@keyframes autoScrollLarge {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-70%);
  }
}

.auto-scroll-ads:hover {
  animation-play-state: paused;
}

/* Ad image hover effects */
.ad-image {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.ad-image:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}
