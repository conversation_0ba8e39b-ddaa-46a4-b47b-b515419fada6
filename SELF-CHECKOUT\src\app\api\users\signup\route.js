import { connectDB } from '@/lib/mongodb';
import bcrypt from 'bcrypt';
import User from '@/models/User';

export async function POST(request) {
    try {
        await connectDB();
        
        const { username, email, password, mobileNumber, role, storename } = await request.json();
        
        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Create new user
        const newUser = new User({
            username,
            email,
            password: hashedPassword,
            mobileNumber,
            role,
            storename
        });
        
        await newUser.save();
        
        return Response.json({
            success: true,
            message: 'User registered!'
        }, { status: 201 });
    } catch (error) {
        console.error('Signup error:', error);
        return Response.json(
            { success: false, message: 'Error: ' + error.message },
            { status: 400 }
        );
    }
}
