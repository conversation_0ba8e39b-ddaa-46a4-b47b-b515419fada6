import mongoose from 'mongoose';

const ProductSchema = new mongoose.Schema({
  productName: {
    type: String,
    required: true,
  },
  barcode: {
    type: String,
    unique: true,
  },
  price: {
    type: Number,
    required: function() {
      return !this.pricePerKg;
    },
  },
  pricePerKg: {
    type: Number,
    required: function() {
      return ['weight'].includes(this.selectedOption);
    },
  },
  quantity: {
    type: Number,
    required: function() {
      return !this.weight;
    },
  },
  weight: {
    type: Number,
    required: function() {
      return ['weight'].includes(this.selectedOption);
    },
  },
  productCode: {
    type: String,
  },
  description: {
    type: String,
  },
  category: {
    type: String,
  },
  supplier: {
    type: String,
  },
  productImage: {
    type: String,
  },
  selectedOption: {
    type: String,
  },
}, {
  timestamps: true,
});

ProductSchema.virtual('status').get(function() {
  if (this.quantity === 0) {
    return 'Out of Stock';
  } else if (this.quantity > 0 && this.quantity <= 10) {
    return 'Limited Stock';
  } else {
    return 'In Stock';
  }
});

ProductSchema.virtual('statusColor').get(function() {
  switch (this.status) {
    case 'Out of Stock':
      return '#e74c3c'; 
    case 'Limited Stock':
      return '#f39c12'; 
    case 'In Stock':
    default:
      return '#2ecc71'; 
  }
});

ProductSchema.set('toJSON', { virtuals: true });
ProductSchema.set('toObject', { virtuals: true });

export default mongoose.models.Product || mongoose.model('Product', ProductSchema);
