import { connectDB } from '@/lib/mongodb';
import Product from '@/models/Product';

export async function GET(request) {
    try {
        await connectDB();
        
        const { searchParams } = new URL(request.url);
        const productName = searchParams.get('productName');
        
        if (!productName) {
            return Response.json(
                { success: false, error: 'Product name is required' },
                { status: 400 }
            );
        }
        
        // Check if product exists by name
        const productExists = await Product.findOne({ productName });
        
        return Response.json({
            success: true,
            exists: !!productExists
        });
    } catch (error) {
        console.error('Product check error:', error);
        return Response.json(
            { success: false, error: 'Error checking product', details: error.message },
            { status: 500 }
        );
    }
}
