import React, { useState, useEffect } from 'react';

const AdsSection = () => {
  const [currentAdIndex, setCurrentAdIndex] = useState(0);

  const ads = [
    {
      image: "/cake.jpg",
      title: "Fresh Cakes",
      subtitle: "Delicious & Fresh Daily",
      alt: "Fresh Cakes"
    },
    {
      image: "/brownie.jpg",
      title: "Chocolate Brownies",
      subtitle: "Rich & Fudgy",
      alt: "Chocolate Brownies"
    },
    {
      image: "/choc.jpg",
      title: "Premium Chocolates",
      subtitle: "Sweet Indulgence",
      alt: "Premium Chocolates"
    },
    {
      image: "/icecreamV.jpg",
      title: "Ice Cream Varieties",
      subtitle: "Cool & Refreshing",
      alt: "Ice Cream Varieties"
    },
    {
      image: "/rmcake.jpg",
      title: "Special Cakes",
      subtitle: "Made with Love",
      alt: "Special Cakes"
    },
    {
      image: "/sweets.png",
      title: "Traditional Sweets",
      subtitle: "Authentic Flavors",
      alt: "Traditional Sweets"
    },
    {
      image: "/brow.jpg",
      title: "Bakery Items",
      subtitle: "Fresh Baked Goods",
      alt: "Bakery Items"
    },
    {
      image: "/rama.jpg",
      title: "Special Offers",
      subtitle: "Limited Time Deals",
      alt: "Special Offers"
    }
  ];

  // Auto-change ads every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAdIndex((prevIndex) => (prevIndex + 1) % ads.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [ads.length]);

  const currentAd = ads[currentAdIndex];

  return (
    <div className="lg:col-span-4 bg-white rounded-xl shadow-lg overflow-hidden h-full">
      {/* Full-size single ad */}
      <div className="relative h-full w-full overflow-hidden">
        <img 
          src={currentAd.image}
          alt={currentAd.alt}
          className="w-full h-full object-cover transition-all duration-1000 ease-in-out"
        />
        
        {/* Overlay content */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex flex-col justify-end p-8">
          <div className="text-white">
            <h4 className="text-4xl font-bold mb-4 transition-all duration-500 ease-in-out">
              {currentAd.title}
            </h4>
            <p className="text-2xl mb-6 transition-all duration-500 ease-in-out">
              {currentAd.subtitle}
            </p>
          </div>
          
          {/* Ad indicators */}
          <div className="flex space-x-2 mb-4">
            {ads.map((_, index) => (
              <div
                key={index}
                className={`h-2 w-8 rounded-full transition-all duration-300 ${
                  index === currentAdIndex ? 'bg-white' : 'bg-white/30'
                }`}
              />
            ))}
          </div>
          
          {/* Powered by */}
          <div className="text-center">
            <p className="text-lg text-white/80 font-medium">Powered by Synecx AI</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsSection;
