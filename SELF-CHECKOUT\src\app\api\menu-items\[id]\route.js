import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import MenuItem1 from '@/models/MenuItem1';

export async function GET(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    const menuItem = await MenuItem1.findById(id);
    
    if (!menuItem) {
      return NextResponse.json({ message: 'Menu item not found' }, { status: 404 });
    }
    
    return NextResponse.json(menuItem);
  } catch (error) {
    console.error('Error fetching menu item:', error);
    return NextResponse.json({ 
      message: 'Error fetching menu item', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function DELETE(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    const deletedMenuItem = await MenuItem1.findByIdAndDelete(id);
    
    if (!deletedMenuItem) {
      return NextResponse.json({ message: 'Menu item not found' }, { status: 404 });
    }
    
    return NextResponse.json({ 
      message: 'Menu item deleted successfully', 
      menuItem: deletedMenuItem 
    });
  } catch (error) {
    console.error('Error deleting menu item:', error);
    return NextResponse.json({ 
      message: 'Error deleting menu item', 
      error: error.message 
    }, { status: 500 });
  }
}

export async function PUT(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    const body = await request.json();
    const {
      productName,
      barcode,
      price,
      quantity,
      shortCode,
      description,
      orderType
    } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    // Validate orderType if provided
    if (orderType && !['Delivery', 'Dine-In', 'Pickup'].includes(orderType)) {
      return NextResponse.json({ 
        error: 'Invalid order type. Must be Delivery, Dine-In, or Pickup' 
      }, { status: 400 });
    }
    
    const updateData = {};
    if (productName !== undefined) updateData.productName = productName;
    if (barcode !== undefined) updateData.barcode = barcode;
    if (price !== undefined) updateData.price = price;
    if (quantity !== undefined) updateData.quantity = quantity;
    if (shortCode !== undefined) updateData.shortCode = shortCode;
    if (description !== undefined) updateData.description = description;
    if (orderType !== undefined) updateData.orderType = orderType;
    
    const updatedMenuItem = await MenuItem1.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!updatedMenuItem) {
      return NextResponse.json({ message: 'Menu item not found' }, { status: 404 });
    }
    
    return NextResponse.json({ 
      message: 'Menu item updated successfully', 
      menuItem: updatedMenuItem 
    });
  } catch (error) {
    console.error('Error updating menu item:', error);
    
    if (error.code === 11000) {
      return NextResponse.json({ 
        message: 'Menu item with this barcode already exists', 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: 'Error updating menu item', 
      error: error.message 
    }, { status: 400 });
  }
}
