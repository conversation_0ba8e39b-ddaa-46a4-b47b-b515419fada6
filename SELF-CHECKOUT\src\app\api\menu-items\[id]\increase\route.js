import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import MenuItem1 from '@/models/MenuItem1';

export async function PATCH(request, { params }) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    const menuItem = await MenuItem1.findById(id);
    
    if (!menuItem) {
      return NextResponse.json({ message: 'Menu item not found' }, { status: 404 });
    }
    
    menuItem.quantity += 1;
    await menuItem.save();
    
    return NextResponse.json({ 
      message: 'Menu item quantity increased', 
      menuItem 
    });
  } catch (error) {
    console.error('Error increasing menu item quantity:', error);
    return NextResponse.json({ 
      message: 'Error increasing menu item quantity', 
      error: error.message 
    }, { status: 500 });
  }
}
