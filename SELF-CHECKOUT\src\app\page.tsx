"use client";

import React, { useState } from "react";
import Cart from "@/components/cart/cart";
import WelcomeOverlay from "@/components/overlay/welcome-overlay";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Home() {
  const [totalPrice, setTotalPrice] = useState(0);
  const [showCart, setShowCart] = useState(false);

  const handleUpdateTotalPrice = (price: number) => {
    setTotalPrice(price);
  };

  const handleProceedToCart = () => {
    setShowCart(true);
  };

  return (
    <div>
      {!showCart ? (
        <WelcomeOverlay onProceedToCart={handleProceedToCart} />
      ) : (
        <Cart onUpdateTotalPrice={handleUpdateTotalPrice} />
      )}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
}
