{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/header.tsx"], "sourcesContent": ["import Image from 'next/image'\r\nimport React from 'react'\r\n\r\nconst Header = () => {\r\n  return (\r\n    <div className='w-full h-20 sticky top-0 flex justify-center items-center'><Image src={'/Logo.svg'} alt={''} width={200} height={100} /></div>\r\n  )\r\n}\r\n\r\nexport default Header"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAI,WAAU;kBAA4D,cAAA,6LAAC,gIAAA,CAAA,UAAK;YAAC,KAAK;YAAa,KAAK;YAAI,OAAO;YAAK,QAAQ;;;;;;;;;;;AAErI;KAJM;uCAMS"}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/ads-section.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst AdsSection = () => {\n  const [currentAdIndex, setCurrentAdIndex] = useState(0);\n\n  const ads = [\n    {\n      image: \"/cake.jpg\",\n      title: \"Fresh Cakes\",\n      subtitle: \"Delicious & Fresh Daily\",\n      alt: \"Fresh Cakes\"\n    },\n    {\n      image: \"/brownie.jpg\",\n      title: \"Chocolate Brownies\",\n      subtitle: \"Rich & Fudgy\",\n      alt: \"Chocolate Brownies\"\n    },\n    {\n      image: \"/choc.jpg\",\n      title: \"Premium Chocolates\",\n      subtitle: \"Sweet Indulgence\",\n      alt: \"Premium Chocolates\"\n    },\n    {\n      image: \"/icecreamV.jpg\",\n      title: \"Ice Cream Varieties\",\n      subtitle: \"Cool & Refreshing\",\n      alt: \"Ice Cream Varieties\"\n    },\n    {\n      image: \"/rmcake.jpg\",\n      title: \"Special Cakes\",\n      subtitle: \"Made with Love\",\n      alt: \"Special Cakes\"\n    },\n    {\n      image: \"/sweets.png\",\n      title: \"Traditional Sweets\",\n      subtitle: \"Authentic Flavors\",\n      alt: \"Traditional Sweets\"\n    },\n    {\n      image: \"/brow.jpg\",\n      title: \"Bakery Items\",\n      subtitle: \"Fresh Baked Goods\",\n      alt: \"Bakery Items\"\n    },\n    {\n      image: \"/rama.jpg\",\n      title: \"Special Offers\",\n      subtitle: \"Limited Time Deals\",\n      alt: \"Special Offers\"\n    }\n  ];\n\n  // Auto-change ads every 5 seconds\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentAdIndex((prevIndex) => (prevIndex + 1) % ads.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [ads.length]);\n\n  const currentAd = ads[currentAdIndex];\n\n  return (\n    <div className=\"lg:col-span-4 bg-white rounded-xl shadow-lg overflow-hidden h-full\">\n      {/* Full-size single ad */}\n      <div className=\"relative h-full w-full overflow-hidden\">\n        <img \n          src={currentAd.image}\n          alt={currentAd.alt}\n          className=\"w-full h-full object-cover transition-all duration-1000 ease-in-out\"\n        />\n        \n        {/* Overlay content */}\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex flex-col justify-end p-8\">\n          <div className=\"text-white\">\n            <h4 className=\"text-4xl font-bold mb-4 transition-all duration-500 ease-in-out\">\n              {currentAd.title}\n            </h4>\n            <p className=\"text-2xl mb-6 transition-all duration-500 ease-in-out\">\n              {currentAd.subtitle}\n            </p>\n          </div>\n          \n          {/* Ad indicators */}\n          <div className=\"flex space-x-2 mb-4\">\n            {ads.map((_, index) => (\n              <div\n                key={index}\n                className={`h-2 w-8 rounded-full transition-all duration-300 ${\n                  index === currentAdIndex ? 'bg-white' : 'bg-white/30'\n                }`}\n              />\n            ))}\n          </div>\n          \n          {/* Powered by */}\n          <div className=\"text-center\">\n            <p className=\"text-lg text-white/80 font-medium\">Powered by Synecx AI</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdsSection;\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,MAAM,aAAa;;IACjB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,MAAM;QACV;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,OAAO;YACP,UAAU;YACV,KAAK;QACP;KACD;IAED,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW;iDAAY;oBAC3B;yDAAkB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,IAAI,MAAM;;gBAC/D;gDAAG;YAEH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG;QAAC,IAAI,MAAM;KAAC;IAEf,MAAM,YAAY,GAAG,CAAC,eAAe;IAErC,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,KAAK,UAAU,KAAK;oBACpB,KAAK,UAAU,GAAG;oBAClB,WAAU;;;;;;8BAIZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,UAAU,KAAK;;;;;;8CAElB,6LAAC;oCAAE,WAAU;8CACV,UAAU,QAAQ;;;;;;;;;;;;sCAKvB,6LAAC;4BAAI,WAAU;sCACZ,IAAI,GAAG,CAAC,CAAC,GAAG,sBACX,6LAAC;oCAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,iBAAiB,aAAa,eACxC;mCAHG;;;;;;;;;;sCASX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7D;GA1GM;KAAA;uCA4GS"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/cart/cart.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect,useRef  } from \"react\";\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Box,\r\n  Button,\r\n  Dialog,\r\n  DialogActions,\r\n  DialogContent,\r\n  DialogTitle,\r\n  TextField,\r\n  List,\r\n  Divider,\r\n  ListItem,\r\n  FormControl,\r\n  MenuItem,\r\n  Select,\r\n  ListItemText,\r\n  Autocomplete,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport LockIcon from \"@mui/icons-material/Lock\";\r\nimport LockOpenIcon from \"@mui/icons-material/LockOpen\";\r\nimport { toast } from \"react-toastify\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faShoppingCart } from \"@fortawesome/free-solid-svg-icons\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport SearchOutlinedIcon from \"@mui/icons-material/SearchOutlined\";\r\nimport AddOutlinedIcon from \"@mui/icons-material/AddOutlined\";\r\nimport Header from \"./header\";\r\nimport AdsSection from \"./ads-section\";\r\n\r\nconst WEIGHT_BASED_ITEMS = new Set([\r\n  \"Milk Assorted\",\r\n  \"Normal Assorted\",\r\n  \"Ghee Assorted\",\r\n  \"Kaju Assorted\",\r\n  \"ARISI-MURUKKU\",\r\n  \"BOMBAY-MIXTURE\",\r\n  \"GARLIC-MIXTURE\",\r\n  \"KAARA-BOONTHI\",\r\n  \"KAARA-MURUKKU\",\r\n  \"KAI-SUTHU-MURUKKU\",\r\n  \"KARA-SEV\",\r\n  \"MASALA KADALAI\",\r\n  \"MASALA-POTATO-CHIPS-GREEN-\",\r\n  \"MASALA-POTATO-CHIPS-RED-\",\r\n  \"NAENDHRAM-CHIPS\",\r\n  \"NORMAL-MIXTURE\",\r\n  \"OTTU-PAKKODA\",\r\n  \"POTATO-CHIPS\",\r\n  \"PUDI-MURUKKU\",\r\n  \"THATTA-MURUKKU\",\r\n  \"CORN\",\r\n  \"BADHAM-BARFI\",\r\n  \"BADHUSHA\",\r\n  \"BANARAS-SANDWICH\",\r\n  \"BESAN-LADDU\",\r\n  \"BOMBAY-HALWA\",\r\n  \"CARROT-MYSORE-PAK\",\r\n  \"CHANDRAKALA\",\r\n  \"DRY-FRUIT-LADDU\",\r\n  \"GHEE-MYSORE-PAK\",\r\n  \"GULAB-JAMUN\",\r\n  \"GULKAN-BARFI\",\r\n  \"HORLICKS-BARFI\",\r\n  \"JILAPI\",\r\n  \"KAJU-KATLI\",\r\n  \"KAJU-PISTHA-ROLL\",\r\n  \"KALA-JAMUN\",\r\n  \"KALAKAN-BARFI\",\r\n  \"LADDU\",\r\n  \"LAMBA-JAMUN\",\r\n  \"MAKAN-PEDA\",\r\n  \"MANGO-KATLI\",\r\n  \"MILK-CAKE\",\r\n  \"MILK-PEDA\",\r\n  \"MOTHI-LADDU\",\r\n  \"MOTHI-PAK\",\r\n  \"MYSORE-PAK\",\r\n  \"RASGULLA\",\r\n  \"SPECIAL-GHEE-SOANPAPDI\",\r\n]);\r\n\r\n\r\nconst Cart = ({ onUpdateTotalPrice }) => {\r\n  const [items, setItems] = useState([]);\r\n  const [displayedItems, setDisplayedItems] = useState([]);\r\n  const [cartItems, setCartItems] = useState([]);\r\n  const [isCartLocked, setIsCartLocked] = useState(false);\r\n  const [isAddMode, setIsAddMode] = useState(false);\r\n\r\n  const [openAddProductDialog, setOpenAddProductDialog] = useState(false);\r\n  const [newProductName, setNewProductName] = useState(\"\");\r\n  const [newProductPrice, setNewProductPrice] = useState(\"\");\r\n  const [newProductQuantity, setNewProductQuantity] = useState(1);\r\n  const [newProductShortcode, setNewProductShortcode] = useState(\"\");\r\n  const [productSuggestions, setProductSuggestions] = useState([]);\r\n  const [productLoading, setProductLoading] = useState(false);\r\n  // Fetch product suggestions for Add Product dialog\r\n  const fetchProductSuggestions = async (query) => {\r\n    setProductLoading(true);\r\n    try {\r\n      const response = await fetch(`/api/search?query=${encodeURIComponent(query)}`);\r\n      if (!response.ok) throw new Error(\"Failed to fetch product suggestions\");\r\n      const data = await response.json();\r\n      setProductSuggestions(data);\r\n    } catch (error) {\r\n      setProductSuggestions([]);\r\n    } finally {\r\n      setProductLoading(false);\r\n    }\r\n  };\r\n\r\n  // When user types in product name, fetch suggestions\r\n  const handleProductNameInput = (event, value, reason) => {\r\n    setNewProductName(value);\r\n    if (value && value.length > 0) {\r\n      fetchProductSuggestions(value);\r\n    } else {\r\n      setProductSuggestions([]);\r\n      setNewProductPrice(\"\");\r\n    }\r\n  };\r\n\r\n  // When user selects a suggestion, update price\r\n  const handleProductSuggestionSelect = (event, value) => {\r\n    if (value && value.productName) {\r\n      setNewProductName(value.productName);\r\n      // Prefer pricePerKg if present, else price\r\n      setNewProductPrice(value.pricePerKg || value.price || \"\");\r\n    } else if (typeof value === \"string\") {\r\n      setNewProductName(value);\r\n      setNewProductPrice(\"\");\r\n    }\r\n  };\r\n  const [weight, setWeight] = useState(0);\r\n  const [clicked, setClicked] = useState(false);\r\n  const [searchOpen, setSearchOpen] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [openSearchDialog, setOpenSearchDialog] = useState(false);\r\n  const [clip_tag, setClipTag] = useState([]);\r\n\r\n  // Motion Detection State and Refs\r\n  const canvasRef = useRef(null);\r\n  const [roi, setRoi] = useState({ x: 210, y: 159, width: 257, height: 337 }); // Updated ROI from cart1.jsx\r\n  const [showMotionToast, setShowMotionToast] = useState(false);\r\n  const [showItemToast, setShowItemToast] = useState(false);\r\n  const motionDetectedRef = useRef(false);\r\n  const framesSinceMotionRef = useRef(0);\r\n  const cooldownRef = useRef(false);\r\n  const [itemPlaced, setItemPlaced] = useState(false); // New state for item placement\r\n\r\n// Calculate total price\r\nconst calculateTotalPrice = (items) => {\r\n  return items.reduce((acc, item) => {\r\n    if (item.weight_flag === 1) {\r\n      return acc + item.price * (item.weight || 0);\r\n    }\r\n    return acc + item.price * (item.quantity || 1);\r\n  }, 0);\r\n};\r\n\r\n// Update total price whenever cart items change\r\nuseEffect(() => {\r\n  const totalPrice = calculateTotalPrice(cartItems);\r\n  if (onUpdateTotalPrice) {\r\n    onUpdateTotalPrice(totalPrice);\r\n  }\r\n}, [cartItems, onUpdateTotalPrice]);\r\n\r\n  // useEffect(() => {\r\n  //   const cellSize = 30;\r\n  //   const diffThreshold = 10000;\r\n  //   const processEveryNthFrame = 3;\r\n  //   let frameCounter = 0;\r\n  //   let previousGrayData = null;\r\n\r\n  //   const fetchAndProcessImage = async () => {\r\n  //     try {\r\n  //       const response = await fetch(\"http://*************:9000/image\");\r\n  //       const blob = await response.blob();\r\n  //       const url = URL.createObjectURL(blob);\r\n  //       // const image = new Image();\r\n  //       const image = new window.Image();\r\n  //       image.src = url;\r\n  //       image.onload = () => {\r\n  //         const canvas = canvasRef.current;\r\n  //         const ctx = canvas.getContext(\"2d\");\r\n  //         canvas.width = image.width;\r\n  //         canvas.height = image.height;\r\n\r\n  //         ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n  //         ctx.drawImage(image, 0, 0);\r\n\r\n  //         frameCounter++;\r\n  //         if (frameCounter % processEveryNthFrame !== 0) {\r\n  //           URL.revokeObjectURL(url);\r\n  //           return;\r\n  //         }\r\n\r\n  //         const { x: roiX, y: roiY, width, height } = roi;\r\n\r\n  //         const currentImageData = ctx.getImageData(roiX, roiY, width, height);\r\n  //         const currentGrayData = toGrayscale(currentImageData);\r\n\r\n  //         if (previousGrayData) {\r\n  //           const motionCells = detectMotion(\r\n  //             previousGrayData,\r\n  //             currentGrayData,\r\n  //             width,\r\n  //             height,\r\n  //             cellSize,\r\n  //             diffThreshold\r\n  //           );\r\n\r\n  //           const motionDetectedNow = motionCells.length > 0;\r\n  //           if (motionDetectedNow && !cooldownRef.current) {\r\n  //             setShowMotionToast(true);\r\n  //             motionDetectedRef.current = true;\r\n  //             framesSinceMotionRef.current = 0;\r\n  //             cooldownRef.current = true;\r\n  //             setTimeout(() => {\r\n  //               setShowMotionToast(false);\r\n  //               cooldownRef.current = false;\r\n  //             }, 500);\r\n  //           } else if (motionDetectedRef.current) {\r\n  //             framesSinceMotionRef.current += 1;\r\n  //             if (framesSinceMotionRef.current >= 2) {\r\n  //               setShowItemToast(true);\r\n  //               motionDetectedRef.current = false;\r\n  //               framesSinceMotionRef.current = 0;\r\n  //               setItemPlaced(true); // Set itemPlaced to true\r\n  //               setTimeout(() => {\r\n  //                 setShowItemToast(false);\r\n  //               }, 1000);\r\n  //             }\r\n  //           }\r\n  //         }\r\n\r\n  //         previousGrayData = currentGrayData;\r\n  //         URL.revokeObjectURL(url);\r\n  //       };\r\n  //     } catch (error) {\r\n  //       console.error(\"Error fetching or processing image:\", error);\r\n  //     }\r\n  //   };\r\n\r\n  //   const interval = setInterval(fetchAndProcessImage, 250);\r\n  //   return () => clearInterval(interval);\r\n  // }, [roi]);\r\n\r\n\r\n //saas \r\n  useEffect(() => {\r\n    const cellSize = 20; // Updated from cart1.jsx\r\n    const diffThreshold = 12500; // Updated from cart1.jsx\r\n    const processEveryNthFrame = 1; // Updated from cart1.jsx - process every frame\r\n    let frameCounter = 0;\r\n    let previousGrayData = null;\r\n\r\n  //   \r\n  const fetchAndProcessImage = async () => {\r\n    try {\r\n      const response = await fetch(\"http://*************:9000/image\");\r\n      const blob = await response.blob();\r\n      const url = URL.createObjectURL(blob);\r\n      // const image = new Image();\r\n      const image = new window.Image();\r\n      image.src = url;\r\n      image.onload = () => {\r\n        const canvas = canvasRef.current;\r\n        const ctx = canvas.getContext(\"2d\");\r\n        canvas.width = image.width;\r\n        canvas.height = image.height;\r\n\r\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\r\n        ctx.drawImage(image, 0, 0);\r\n\r\n        frameCounter++;\r\n        if (frameCounter % processEveryNthFrame !== 0) {\r\n          URL.revokeObjectURL(url);\r\n          return;\r\n        }\r\n\r\n        const { x: roiX, y: roiY, width, height } = roi;\r\n\r\n        const currentImageData = ctx.getImageData(roiX, roiY, width, height);\r\n        const currentGrayData = toGrayscale(currentImageData);\r\n\r\n        if (previousGrayData) {\r\n          const motionCells = detectMotion(\r\n            previousGrayData,\r\n            currentGrayData,\r\n            width,\r\n            height,\r\n            cellSize,\r\n            diffThreshold\r\n          );\r\n\r\n          const motionDetectedNow = motionCells.length > 0;\r\n          if (motionDetectedNow && !cooldownRef.current) {\r\n            setShowMotionToast(true);\r\n            motionDetectedRef.current = true;\r\n            cooldownRef.current = true;\r\n\r\n            // Immediately call the prediction API\r\n            captureAndSendImage();\r\n\r\n            setTimeout(() => {\r\n              setShowMotionToast(false);\r\n              cooldownRef.current = false;\r\n            }, 300); // Updated cooldown from cart1.jsx\r\n          }\r\n        }\r\n\r\n        previousGrayData = currentGrayData;\r\n        URL.revokeObjectURL(url);\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching or processing image:\", error);\r\n    }\r\n  };\r\n\r\n  const interval = setInterval(fetchAndProcessImage, 250); // Updated interval from cart1.jsx\r\n  return () => clearInterval(interval);\r\n}, [roi]);\r\n\r\n  useEffect(() => {\r\n    if (itemPlaced) {\r\n      captureAndSendImage();\r\n      setItemPlaced(false); // Reset itemPlaced after prediction\r\n    }\r\n  }, [itemPlaced]);\r\n\r\n  const toGrayscale = (imageData) => {\r\n    const { data, width, height } = imageData;\r\n    const grayData = new Uint8ClampedArray(width * height);\r\n    for (let i = 0; i < data.length; i += 4) {\r\n      const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];\r\n      grayData[i / 4] = gray;\r\n    }\r\n    return grayData;\r\n  };\r\n\r\n  const detectMotion = (prevGray, currentGray, width, height, cellSize, threshold) => {\r\n    const motionCells = [];\r\n    for (let y = 0; y <= height - cellSize; y += cellSize) {\r\n      for (let x = 0; x <= width - cellSize; x += cellSize) {\r\n        let cellDiff = 0;\r\n        for (let i = y; i < y + cellSize; i++) {\r\n          for (let j = x; j < x + cellSize; j++) {\r\n            const index = i * width + j;\r\n            if (index < prevGray.length) {\r\n              cellDiff += Math.abs(prevGray[index] - currentGray[index]);\r\n            }\r\n          }\r\n        }\r\n        if (cellDiff > threshold) {\r\n          motionCells.push({ x, y });\r\n        }\r\n      }\r\n    }\r\n    return motionCells;\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      const savedItems = localStorage.getItem(\"cartItems\");\r\n      setCartItems(savedItems ? JSON.parse(savedItems) : []);\r\n    }\r\n  }, []);\r\n  const subtotal = cartItems\r\n    .reduce((acc, item) => {\r\n      if (item.weight_flag === 1) {\r\n        return acc + item.price * (item.weight || 0);\r\n      }\r\n      return acc + item.price * (item.quantity || 1);\r\n    }, 0)\r\n    .toFixed(2);\r\n\r\n\r\n  const totalAmount = Math.round(parseFloat(subtotal));\r\n\r\n  const handleDropdownChange = async (index, newProductName) => {\r\n    try {\r\n      const isWeightBased = WEIGHT_BASED_ITEMS.has(newProductName);\r\n      const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n      const priceResponse = await fetch(\r\n        `/api/${priceEndpoint}?productName=${encodeURIComponent(\r\n          newProductName\r\n        )}`\r\n      );\r\n\r\n      if (!priceResponse.ok) {\r\n        throw new Error(\r\n          `Failed to fetch price for ${newProductName}: ${priceResponse.statusText}`\r\n        );\r\n      }\r\n\r\n      const priceData = await priceResponse.json();\r\n      const newPrice = isWeightBased\r\n        ? priceData.pricePerKg\r\n        : priceData.pricePer;\r\n\r\n      setCartItems((prevCartItems) => {\r\n        const updatedCartItems = prevCartItems.map((item, i) => {\r\n          if (i === index) {\r\n            const updatedItem = {\r\n              ...item,\r\n              selectedProduct: newProductName,\r\n              productName: newProductName,\r\n              price: newPrice,\r\n              weight_flag: isWeightBased ? 1 : 0,\r\n            };\r\n\r\n            if (item.weight_flag !== (isWeightBased ? 1 : 0)) {\r\n              if (isWeightBased) {\r\n                delete updatedItem.quantity;\r\n                updatedItem.weight = 0;\r\n              } else {\r\n                delete updatedItem.weight;\r\n                updatedItem.quantity = 1;\r\n              }\r\n            }\r\n\r\n            return updatedItem;\r\n          }\r\n          return item;\r\n        });\r\n\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error updating product price:\", error);\r\n      toast.error(\"Failed to update product price\");\r\n    }\r\n  };\r\n \r\n\r\n  // \r\n  // const captureAndSendImage = useCallback(async () => {\r\n  //   try {\r\n  //     toast.info(\"Capturing image...\", { autoClose: 800 });\r\n\r\n  //     const response = await fetch(\"http://*************:9000/image\");\r\n  //     if (!response.ok) {\r\n  //       throw new Error(`Image capture failed: ${response.statusText}`);\r\n  //     }\r\n  //     const blob = await response.blob();\r\n\r\n  //     const formData = new FormData();\r\n  //     formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n\r\n  //     toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n\r\n  //     const predictResponse = await fetch(\"https://shrew-golden-toucan.ngrok-free.app/predict\", {\r\n  //       method: \"POST\",\r\n  //       body: formData,\r\n  //     });\r\n\r\n  //     if (!predictResponse.ok) {\r\n  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n  //     }\r\n\r\n  //     const data = await predictResponse.json();\r\n  //     console.log(data);\r\n  //     const { yolo_tag, clip_tag, weight_flag } = data;\r\n\r\n  //     const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n  //     if (!weightResponse.ok) {\r\n  //       throw new Error(\"Failed to fetch weight\");\r\n  //     }\r\n  //     const weightData = await weightResponse.json();\r\n  //     const weight = weightData.weight;\r\n\r\n  //     setClipTag(clip_tag);\r\n  //     console.log(\"Clip Tag: \", clip_tag);\r\n\r\n  //     // Update cart items based on prediction\r\n  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n  //     const newCartItems = await Promise.all(\r\n  //       tags.map(async (productName, index) => {\r\n  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n  //         const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n  //         const priceResponse = await fetch(\r\n  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n  //         );\r\n\r\n  //         if (!priceResponse.ok) {\r\n  //           throw new Error(`Failed to fetch price for ${productName}`);\r\n  //         }\r\n\r\n  //         const priceData = await priceResponse.json();\r\n  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n\r\n  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n\r\n  //         return {\r\n  //           productName,\r\n  //           weight_flag: isWeightBased ? 1 : 0,\r\n  //           alternatives: [productName, ...alternatives],\r\n  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),\r\n  //         };\r\n  //       })\r\n  //     );\r\n\r\n  //     // Remove items not in the predict response\r\n  //     setCartItems((prevCartItems) => {\r\n  //       const updatedCartItems = prevCartItems.filter((item) =>\r\n  //         tags.includes(item.productName)\r\n  //       );\r\n\r\n  //       // Add new items from the predict response\r\n  //       newCartItems.forEach((newItem) => {\r\n  //         const existingItemIndex = updatedCartItems.findIndex(\r\n  //           (item) => item.productName === newItem.productName\r\n  //         );\r\n\r\n  //         if (existingItemIndex > -1) {\r\n  //           const existingItem = updatedCartItems[existingItemIndex];\r\n  //           if (newItem.weight_flag === 1) {\r\n  //             updatedCartItems[existingItemIndex] = {\r\n  //               ...existingItem,\r\n  //               weight: (existingItem.weight || 0) + (newItem.weight || 0),\r\n  //             };\r\n  //           } else {\r\n  //             updatedCartItems[existingItemIndex] = {\r\n  //               ...existingItem,\r\n  //               quantity: (existingItem.quantity || 1) + (newItem.quantity || 1),\r\n  //             };\r\n  //           }\r\n  //         } else {\r\n  //           updatedCartItems.push(newItem);\r\n  //         }\r\n  //       });\r\n\r\n  //       localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  //       return updatedCartItems;\r\n  //     });\r\n\r\n  //     setWeight(weight);\r\n  //   } catch (error) {\r\n  //     console.error(\"Error capturing or sending image:\", error);\r\n  //   }\r\n  // }, []);\r\n  \r\n\r\n\r\n\r\n  // const captureAndSendImage = useCallback(async () => {\r\n  //   try {\r\n  //     toast.info(\"Capturing image...\", { autoClose: 800 });\r\n  \r\n  //     const response = await fetch(\"http://*************:9000/image\");\r\n  //     if (!response.ok) {\r\n  //       throw new Error(`Image capture failed: ${response.statusText}`);\r\n  //     }\r\n  //     const blob = await response.blob();\r\n  \r\n  //     const formData = new FormData();\r\n  //     formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n  \r\n  //     toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n  \r\n  //     const predictResponse = await fetch(\"https://shrew-golden-toucan.ngrok-free.app/predict\", {\r\n  //       method: \"POST\",\r\n  //       body: formData,\r\n  //     });\r\n  \r\n  //     if (!predictResponse.ok) {\r\n  //       throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n  //     }\r\n  \r\n  //     const data = await predictResponse.json();\r\n  //     console.log(data);\r\n  //     const { yolo_tag, clip_tag, weight_flag } = data;\r\n  \r\n  //     const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n  //     if (!weightResponse.ok) {\r\n  //       throw new Error(\"Failed to fetch weight\");\r\n  //     }\r\n  //     const weightData = await weightResponse.json();\r\n  //     const weight = weightData.weight;\r\n  \r\n  //     setClipTag(clip_tag);\r\n  //     console.log(\"Clip Tag: \", clip_tag);\r\n  \r\n  //     // Process predicted items\r\n  //     const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n  //     const newCartItems = await Promise.all(\r\n  //       tags.map(async (productName, index) => {\r\n  //         const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n  //         const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n  //         const priceResponse = await fetch(\r\n  //           `http://localhost:5000/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n  //         );\r\n  \r\n  //         if (!priceResponse.ok) {\r\n  //           throw new Error(`Failed to fetch price for ${productName}`);\r\n  //         }\r\n  \r\n  //         const priceData = await priceResponse.json();\r\n  //         const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n  \r\n  //         const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n  \r\n  //         return {\r\n  //           productName,\r\n  //           weight_flag: isWeightBased ? 1 : 0,\r\n  //           alternatives: [productName, ...alternatives],\r\n  //           ...(isWeightBased ? { weight: weight || 0, price: price || 0 } : { quantity: 1, price: price || 0 }),\r\n  //           fromPredict: true  // Mark items from prediction\r\n  //         };\r\n  //       })\r\n  //     );\r\n  \r\n  //     // Update cart items\r\n  //     setCartItems((prevCartItems) => {\r\n  //       // Keep manually added items\r\n  //       const manualItems = prevCartItems.filter(item => !item.fromPredict);\r\n        \r\n  //       // Merge predicted items with existing quantities/weights\r\n  //       const predictedItems = newCartItems.map(newItem => {\r\n  //         const existingItem = prevCartItems.find(\r\n  //           item => item.productName === newItem.productName && item.fromPredict\r\n  //         );\r\n          \r\n  //         if (existingItem) {\r\n  //           // Preserve existing quantity/weight for previously predicted items\r\n  //           if (newItem.weight_flag === 1) {\r\n  //             return {\r\n  //               ...newItem,\r\n  //               weight: existingItem.weight\r\n  //             };\r\n  //           } else {\r\n  //             return {\r\n  //               ...newItem,\r\n  //               quantity: existingItem.quantity\r\n  //             };\r\n  //           }\r\n  //         }\r\n  //         return newItem;\r\n  //       });\r\n\r\n  //       // Combine manual and predicted items\r\n  //       const updatedCartItems = [...manualItems, ...predictedItems];\r\n  //       localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  //       return updatedCartItems;\r\n  //     });\r\n  \r\n  //     setWeight(weight);\r\n  //   } catch (error) {\r\n  //     console.error(\"Error capturing or sending image:\", error);\r\n  //   }\r\n  // }, []);\r\n\r\n\r\n  const captureAndSendImage = useCallback(async () => {\r\n    try {\r\n      toast.info(\"Capturing image...\", { autoClose: 800 });\r\n\r\n      const response = await fetch(\"http://*************:9000/image\");\r\n      if (!response.ok) {\r\n        throw new Error(`Image capture failed: ${response.statusText}`);\r\n      }\r\n      const blob = await response.blob();\r\n\r\n      const formData = new FormData();\r\n      formData.append(\"image\", blob, \"raspberrypi.jpg\");\r\n\r\n      toast.info(\"Sending image for prediction...\", { autoClose: 800 });\r\n\r\n      const predictResponse = await fetch(\"http://*************:9000/predict\", {\r\n        method: \"POST\",\r\n        body: formData,\r\n      });\r\n\r\n      if (!predictResponse.ok) {\r\n        throw new Error(`Prediction request failed: ${predictResponse.statusText}`);\r\n      }\r\n\r\n      const data = await predictResponse.json();\r\n      console.log(data);\r\n      const { yolo_tag, clip_tag, weight_flag } = data;\r\n\r\n      const weightResponse = await fetch(\"http://*************:9000/weight\");\r\n      if (!weightResponse.ok) {\r\n        throw new Error(\"Failed to fetch weight\");\r\n      }\r\n      const weightData = await weightResponse.json();\r\n      const weight = weightData.weight;\r\n\r\n      setClipTag(clip_tag);\r\n      console.log(\"Clip Tag: \", clip_tag);\r\n\r\n      // Process predicted items\r\n      const tags = Array.isArray(yolo_tag) ? yolo_tag : [yolo_tag];\r\n\r\n      // Calculate product counts here, after tags is defined\r\n      const productCounts = tags.reduce((acc, product) => {\r\n        acc[product] = (acc[product] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      const newCartItems = await Promise.all(\r\n        tags.map(async (productName, index) => {\r\n          const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n          const priceEndpoint = isWeightBased ? \"pricePerKg\" : \"pricePer\";\r\n          const priceResponse = await fetch(\r\n            `/api/${priceEndpoint}?productName=${encodeURIComponent(productName)}`\r\n          );\r\n\r\n          if (!priceResponse.ok) {\r\n            throw new Error(`Failed to fetch price for ${productName}`);\r\n          }\r\n\r\n          const priceData = await priceResponse.json();\r\n          const price = isWeightBased ? priceData.pricePerKg : priceData.pricePer;\r\n\r\n          const alternatives = clip_tag[index].filter((tag) => tag !== productName);\r\n\r\n          return {\r\n            productName,\r\n            weight_flag: isWeightBased ? 1 : 0,\r\n            alternatives: [productName, ...alternatives],\r\n            ...(isWeightBased\r\n              ? { weight: (weight || 0) * productCounts[productName], price: price || 0 }\r\n              : { quantity: productCounts[productName], price: price || 0 }\r\n            ),\r\n            fromPredict: true\r\n          };\r\n        })\r\n      );\r\n\r\n      // Update cart items based on lock status or add mode\r\n      setCartItems((prevCartItems) => {\r\n        if (isCartLocked || isAddMode) {\r\n          // When locked or in add mode, preserve all existing items and only add truly new items\r\n          const updatedCartItems = [...prevCartItems];\r\n\r\n          newCartItems.forEach((newItem) => {\r\n            const existingItemIndex = updatedCartItems.findIndex(\r\n              (item) => item.productName === newItem.productName\r\n            );\r\n\r\n            if (existingItemIndex === -1) {\r\n              // Only add if it's a completely new item not in cart\r\n              updatedCartItems.push({\r\n                ...newItem,\r\n                fromPredict: true,\r\n                addedInAddMode: isAddMode\r\n              });\r\n            } else if (isAddMode) {\r\n              // In add mode, increase quantity/weight of existing items\r\n              const existingItem = updatedCartItems[existingItemIndex];\r\n              if (newItem.weight_flag === 1) {\r\n                updatedCartItems[existingItemIndex] = {\r\n                  ...existingItem,\r\n                  weight: (existingItem.weight || 0) + (newItem.weight || 0)\r\n                };\r\n              } else {\r\n                updatedCartItems[existingItemIndex] = {\r\n                  ...existingItem,\r\n                  quantity: (existingItem.quantity || 1) + (newItem.quantity || 1)\r\n                };\r\n              }\r\n            }\r\n            // If locked and item exists, don't modify it - keep existing quantities/weights\r\n          });\r\n\r\n          localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n          return updatedCartItems;\r\n        } else {\r\n          // When not locked, use original logic (replace predicted items)\r\n          const manualItems = prevCartItems.filter(item => !item.fromPredict);\r\n\r\n          const uniquePredictedItems = Array.from(new Set(newCartItems.map(item => item.productName)))\r\n            .map(productName => {\r\n              const item = newCartItems.find(item => item.productName === productName);\r\n              const existingItem = prevCartItems.find(\r\n                prevItem => prevItem.productName === productName && prevItem.fromPredict\r\n              );\r\n\r\n              if (existingItem) {\r\n                if (item.weight_flag === 1) {\r\n                  return {\r\n                    ...item,\r\n                    weight: existingItem.weight\r\n                  };\r\n                } else {\r\n                  return {\r\n                    ...item,\r\n                    quantity: existingItem.quantity\r\n                  };\r\n                }\r\n              }\r\n              return item;\r\n            });\r\n\r\n          const updatedCartItems = [...manualItems, ...uniquePredictedItems];\r\n          localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n          return updatedCartItems;\r\n        }\r\n      });\r\n\r\n      setWeight(weight);\r\n    } catch (error) {\r\n      console.error(\"Error capturing or sending image:\", error);\r\n    }\r\n  }, [isCartLocked, isAddMode]);\r\n\r\n// Modified addProduct function\r\nconst addProduct = (product) => {\r\n  setCartItems((prevCartItems) => {\r\n    const updatedCartItems = [...prevCartItems];\r\n    const existingItemIndex = updatedCartItems.findIndex(\r\n      (item) => item.productName === product.productName && !item.fromPredict\r\n    );\r\n\r\n    if (existingItemIndex === -1) {\r\n      // Add new item without fromPredict flag\r\n      updatedCartItems.push({\r\n        ...product,\r\n        fromPredict: false\r\n      });\r\n    } else {\r\n      // Update existing manual item\r\n      const existingItem = updatedCartItems[existingItemIndex];\r\n      if (product.weight_flag === 1) {\r\n        updatedCartItems[existingItemIndex] = {\r\n          ...existingItem,\r\n          weight: (existingItem.weight || 0) + (product.weight || 0)\r\n        };\r\n      } else {\r\n        updatedCartItems[existingItemIndex] = {\r\n          ...existingItem,\r\n          quantity: (existingItem.quantity || 1) + (product.quantity || 1)\r\n        };\r\n      }\r\n    }\r\n\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n    return updatedCartItems;\r\n  });\r\n};\r\n  const handleIncreaseQuantity = (productName) => {\r\n    const cartItems = JSON.parse(localStorage.getItem(\"cartItems\")) || [];\r\n    const updatedCartItems = cartItems.map((item) =>\r\n      item.productName === productName\r\n        ? { ...item, quantity: (item.quantity || 1) + 1 }\r\n        : item\r\n    );\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n    setCartItems(updatedCartItems);\r\n  };\r\n\r\n  const handleDecreaseQuantity = (productName) => {\r\n    setCartItems((prevItems) => {\r\n      const updatedCartItems = prevItems\r\n        .map((item) => {\r\n          if (item.productName === productName) {\r\n            const newQuantity = (item.quantity || 1) - 1;\r\n            return newQuantity > 0 ? { ...item, quantity: newQuantity } : null;\r\n          }\r\n          return item;\r\n        })\r\n        .filter(Boolean);\r\n\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n      return updatedCartItems;\r\n    });\r\n  };\r\n\r\n  const handleRemoveTag = (tagToRemove) => {\r\n    setCartItems((prevCartItems) => {\r\n      const indexToRemove = prevCartItems.findIndex(\r\n        (item) => item.productName === tagToRemove\r\n      );\r\n      if (indexToRemove === -1) return prevCartItems;\r\n\r\n      const itemToRemove = prevCartItems[indexToRemove];\r\n\r\n      // If cart is locked and item is locked, don't remove\r\n      if (isCartLocked && itemToRemove.isLocked) {\r\n        toast.warning(\"Cannot remove locked item. Unlock cart first.\");\r\n        return prevCartItems;\r\n      }\r\n\r\n      const updatedCartItems = [\r\n        ...prevCartItems.slice(0, indexToRemove),\r\n        ...prevCartItems.slice(indexToRemove + 1),\r\n      ];\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n\r\n      return updatedCartItems;\r\n    });\r\n  };\r\n\r\n  const handleAddToCart = async (item) => {\r\n    // Ensure productName is set (for search results)\r\n    const productName = item.productName || item.name || \"\";\r\n    if (!productName) {\r\n      toast.error(\"Product name is missing.\");\r\n      return;\r\n    }\r\n    const isWeightBased = WEIGHT_BASED_ITEMS.has(productName);\r\n    let currentWeight = 0;\r\n\r\n    if (isWeightBased) {\r\n      try {\r\n        const response = await fetch(\"http://192.168.1.50:9000/weight\");\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          currentWeight = data.weight || 0;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching weight:\", error);\r\n        toast.error(\"Failed to get weight from scale\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    let updatedCartItems;\r\n\r\n    if (isWeightBased) {\r\n      updatedCartItems = [\r\n        ...cartItems,\r\n        {\r\n          ...item,\r\n          productName,\r\n          weight_flag: 1,\r\n          alternatives: [productName],\r\n          weight: currentWeight,\r\n          price: item.pricePerKg || item.price || 0,\r\n        },\r\n      ];\r\n      toast.info(`Added ${currentWeight}KG of ${productName} to cart`);\r\n    } else {\r\n      const existingItemIndex = cartItems.findIndex(\r\n        (cartItem) =>\r\n          cartItem.productName === productName && !cartItem.weight_flag\r\n      );\r\n\r\n      if (existingItemIndex > -1) {\r\n        updatedCartItems = cartItems.map((cartItem, index) => {\r\n          if (index === existingItemIndex) {\r\n            const newQuantity = (cartItem.quantity || 1) + 1;\r\n            toast.info(\r\n              `Updated ${productName} quantity to ${newQuantity}`\r\n            );\r\n            return {\r\n              ...cartItem,\r\n              quantity: newQuantity,\r\n              price: item.price || 0,\r\n            };\r\n          }\r\n          return cartItem;\r\n        });\r\n      } else {\r\n        updatedCartItems = [\r\n          ...cartItems,\r\n          {\r\n            ...item,\r\n            productName,\r\n            weight_flag: 0,\r\n            alternatives: [productName],\r\n            quantity: 1,\r\n            price: item.price || 0,\r\n          },\r\n        ];\r\n        toast.info(`Added ${productName} to cart`);\r\n      }\r\n    }\r\n\r\n    setCartItems(updatedCartItems);\r\n    localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n  };\r\n\r\n  const handleOpenAddProductDialog = () => {\r\n    setOpenAddProductDialog(true);\r\n  };\r\n\r\n  const handleCloseAddProductDialog = () => {\r\n    setOpenAddProductDialog(false);\r\n    setNewProductName(\"\");\r\n    setNewProductPrice(\"\");\r\n    setNewProductQuantity(1);\r\n    setNewProductShortcode(\"\");\r\n  };\r\n\r\n  const handleAddProductSubmit = () => {\r\n    if (!newProductName || !newProductPrice) {\r\n      setError(\"Please enter a product name and price.\");\r\n      return;\r\n    }\r\n\r\n    const newProduct = {\r\n      _id: Date.now().toString(),\r\n      productName: newProductName,\r\n      price: parseFloat(newProductPrice),\r\n      quantity: newProductQuantity,\r\n      shortcode: newProductShortcode,\r\n    };\r\n\r\n    setItems([...items, newProduct]);\r\n    handleAddToCart(newProduct);\r\n    handleCloseAddProductDialog();\r\n  };\r\n\r\n\r\n  // Modified scan logic\r\n  const handleClick = () => {\r\n    captureAndSendImage();\r\n    setTimeout(() => setClicked(false), 600);\r\n  };\r\n\r\n  const handleClearCart = () => {\r\n    setCartItems([]); // Clear cart items state\r\n    localStorage.removeItem(\"cartItems\"); // Remove from localStorage\r\n    toast.success(\"Cart cleared successfully!\"); // Optional success message\r\n  };\r\n\r\n  const handleToggleLock = () => {\r\n    const newLockState = !isCartLocked;\r\n    setIsCartLocked(newLockState);\r\n\r\n    if (newLockState) {\r\n      // When locking, mark all current items as locked and disable add mode\r\n      setIsAddMode(false);\r\n      setCartItems((prevCartItems) => {\r\n        const updatedCartItems = prevCartItems.map(item => ({\r\n          ...item,\r\n          isLocked: true,\r\n          lockedAt: Date.now()\r\n        }));\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n      toast.success(\"Cart locked! Items will not be removed when taken from tray.\");\r\n    } else {\r\n      // When unlocking, remove lock markers\r\n      setCartItems((prevCartItems) => {\r\n        const updatedCartItems = prevCartItems.map(item => {\r\n          const { isLocked, lockedAt, ...itemWithoutLock } = item;\r\n          return itemWithoutLock;\r\n        });\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n      toast.info(\"Cart unlocked! Items will be updated based on tray contents.\");\r\n    }\r\n  };\r\n\r\n  const handleToggleAddMode = () => {\r\n    const newAddMode = !isAddMode;\r\n    setIsAddMode(newAddMode);\r\n\r\n    if (newAddMode) {\r\n      // When enabling add mode, disable lock mode\r\n      setIsCartLocked(false);\r\n      setCartItems((prevCartItems) => {\r\n        const updatedCartItems = prevCartItems.map(item => {\r\n          const { isLocked, lockedAt, ...itemWithoutLock } = item;\r\n          return itemWithoutLock;\r\n        });\r\n        localStorage.setItem(\"cartItems\", JSON.stringify(updatedCartItems));\r\n        return updatedCartItems;\r\n      });\r\n      toast.success(\"Add Mode enabled! New items will be added to cart without removing existing ones.\");\r\n    } else {\r\n      toast.info(\"Add Mode disabled! Cart will update based on tray contents.\");\r\n    }\r\n  };\r\n\r\n  const fetchSearchResults = async (query) => {\r\n    try {\r\n      const response = await fetch(\r\n        `/api/search?query=${encodeURIComponent(query)}`\r\n      );\r\n      if (!response.ok) throw new Error(\"Failed to fetch search results\");\r\n\r\n      const data = await response.json();\r\n      const formattedResults = data.map((item) => ({\r\n        name: item.productName, // Ensure this matches your API field\r\n        price: item.price, // Ensure this matches your API field\r\n      }));\r\n\r\n      setDisplayedItems(formattedResults);\r\n    } catch (error) {\r\n      console.error(\"Error fetching search results:\", error);\r\n    }\r\n  };\r\n\r\n  const handleSearch = (query) => {\r\n    setSearchQuery(query);\r\n    if (query.trim() === \"\") {\r\n      setDisplayedItems([]);\r\n      return;\r\n    }\r\n    fetchSearchResults(query);\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const query = e.target.value;\r\n    setSearchQuery(query);\r\n    handleSearch(query);\r\n  };\r\n\r\n  const handleWeightChange = (productName, newWeight) => {\r\n    setCartItems((prevItems) => {\r\n      const updatedItems = prevItems.map((item) => {\r\n        if (item.productName === productName) {\r\n          const weight = Math.max(\r\n            0,\r\n            Math.round(parseFloat(newWeight || 0) * 1000) / 1000\r\n          );\r\n          return {\r\n            ...item,\r\n            weight: weight,\r\n          };\r\n        }\r\n        return item;\r\n      });\r\n\r\n      localStorage.setItem(\"cartItems\", JSON.stringify(updatedItems));\r\n      return updatedItems;\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div className=\"w-full max-w-full mx-auto px-4 py-6\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-6 h-[calc(100vh-48px)]\">\r\n          {/* Left Column - Cart Details (3/5 width) */}\r\n          <div className=\"lg:col-span-3 bg-white rounded-2xl shadow-xl p-6 overflow-hidden flex flex-col min-h-[600px]\">\r\n            <div className=\"flex items-center justify-between mb-7\">\r\n              <h2 className=\"text-2xl font-bold text-gray-800 flex items-center\">\r\n                <FontAwesomeIcon icon={faShoppingCart} className=\"mr-3 text-blue-600 text-3xl\" />\r\n                Your Cart <span className=\"ml-2 text-xl font-semibold text-blue-700\">({cartItems.length} items)</span>\r\n                {isCartLocked && (\r\n                  <span className=\"ml-3 px-2 py-1 bg-orange-100 text-orange-800 text-sm font-medium rounded-full flex items-center\">\r\n                    <LockIcon style={{ fontSize: 16, marginRight: 4 }} />\r\n                    Locked\r\n                  </span>\r\n                )}\r\n              </h2>\r\n              <div className=\"flex gap-2\">\r\n                <IconButton\r\n                  onClick={() => setOpenSearchDialog(true)}\r\n                  color=\"primary\"\r\n                  size=\"large\"\r\n                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #1976d2', background: '#f5faff', mr: 1 }}\r\n                >\r\n                  <SearchOutlinedIcon style={{ fontSize: 28 }} />\r\n                </IconButton>\r\n                <IconButton\r\n                  onClick={() => setOpenAddProductDialog(true)}\r\n                  color=\"primary\"\r\n                  size=\"large\"\r\n                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #1976d2', background: '#f5faff', mr: 1 }}\r\n                >\r\n                  <AddOutlinedIcon style={{ fontSize: 28 }} />\r\n                </IconButton>\r\n                <IconButton\r\n                  onClick={handleToggleLock}\r\n                  color={isCartLocked ? \"warning\" : \"default\"}\r\n                  size=\"large\"\r\n                  sx={{\r\n                    width: 48,\r\n                    height: 48,\r\n                    borderRadius: 2,\r\n                    border: isCartLocked ? '1px solid #ff9800' : '1px solid #9e9e9e',\r\n                    background: isCartLocked ? '#fff8e1' : '#f5f5f5',\r\n                    mr: 1\r\n                  }}\r\n                  title={isCartLocked ? \"Cart is locked - click to unlock\" : \"Cart is unlocked - click to lock\"}\r\n                >\r\n                  {isCartLocked ? <LockIcon style={{ fontSize: 28 }} /> : <LockOpenIcon style={{ fontSize: 28 }} />}\r\n                </IconButton>\r\n                <IconButton\r\n                  onClick={handleClearCart}\r\n                  color=\"error\"\r\n                  size=\"large\"\r\n                  sx={{ width: 48, height: 48, borderRadius: 2, border: '1px solid #e57373', background: '#fff5f5' }}\r\n                >\r\n                  <DeleteIcon style={{ fontSize: 28 }} />\r\n                </IconButton>\r\n              </div>\r\n            </div>\r\n            {/* Empty Cart State */}\r\n            {cartItems.length === 0 && (\r\n              <div className=\"flex-1 flex flex-col items-center justify-center text-gray-500 py-10\">\r\n                <FontAwesomeIcon icon={faShoppingCart} className=\"text-[60px] mb-5 text-gray-300\" />\r\n                <h3 className=\"text-xl font-bold mb-2\">Your cart is empty</h3>\r\n                <p className=\"text-lg text-center mb-4\">Add some items to get started!</p>\r\n                <Button\r\n                  onClick={() => setOpenSearchDialog(true)}\r\n                  variant=\"contained\"\r\n                  sx={{ fontSize: 18, px: 3, py: 2, borderRadius: 2 }}\r\n                  startIcon={<SearchOutlinedIcon style={{ fontSize: 24 }} />}\r\n                >\r\n                  Browse Products\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {/* Cart Items List */}\r\n            {cartItems.length > 0 && (\r\n              <div className=\"flex-1 overflow-hidden\">\r\n                <div className=\"h-full overflow-y-auto pr-2 space-y-5 custom-scrollbar\">\r\n                  {cartItems.map((item, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"bg-gray-50 rounded-2xl p-6 border border-gray-300 hover:shadow-xl transition-shadow min-h-[90px] flex flex-col justify-center\"\r\n                      style={{ minHeight: 90 }}\r\n                    >\r\n                      <div className=\"flex items-start justify-between mb-4\">\r\n                        <div className=\"flex-1 min-w-0 flex items-center\">\r\n                          <FormControl size=\"medium\" className=\"w-full max-w-sm\">\r\n                            <Select\r\n                              value={item.selectedProduct || item.productName || \"\"}\r\n                              onChange={(e) =>\r\n                                handleDropdownChange(index, e.target.value)\r\n                              }\r\n                              displayEmpty\r\n                              className=\"text-lg\"\r\n                              sx={{ fontSize: 18, minHeight: 40 }}\r\n                            >\r\n                              {item.alternatives &&\r\n                                item.alternatives.map((alternative, i) => (\r\n                                  <MenuItem key={i} value={alternative} style={{ fontSize: 18, minHeight: 32 }}>\r\n                                    {alternative}\r\n                                  </MenuItem>\r\n                                ))}\r\n                            </Select>\r\n                          </FormControl>\r\n                          {isCartLocked && item.isLocked && (\r\n                            <span className=\"ml-2 px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded flex items-center\">\r\n                              <LockIcon style={{ fontSize: 12, marginRight: 2 }} />\r\n                              Locked\r\n                            </span>\r\n                          )}\r\n                          {isCartLocked && !item.isLocked && (\r\n                            <span className=\"ml-2 px-2 py-1 bg-green-100 text-green-600 text-xs font-medium rounded flex items-center\">\r\n                              New\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                        <IconButton\r\n                          onClick={() => handleRemoveTag(item.productName)}\r\n                          size=\"medium\"\r\n                          className=\"text-red-500 hover:bg-red-50 ml-2\"\r\n                          sx={{ width: 38, height: 38 }}\r\n                          disabled={isCartLocked && item.isLocked}\r\n                          title={isCartLocked && item.isLocked ? \"Cannot remove locked item\" : \"Remove item\"}\r\n                        >\r\n                          <DeleteIcon style={{ fontSize: 22 }} />\r\n                        </IconButton>\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-5\">\r\n                          {item.weight_flag === 1 ? (\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-lg text-gray-700 font-semibold\">Weight:</span>\r\n                              <TextField\r\n                                type=\"number\"\r\n                                value={parseFloat(item.weight || 0)}\r\n                                onChange={(e) =>\r\n                                  handleWeightChange(\r\n                                    item.productName,\r\n                                    e.target.value\r\n                                  )\r\n                                }\r\n                                size=\"medium\"\r\n                                className=\"w-24 text-lg\"\r\n                                inputProps={{ style: { fontSize: 16, height: 32 } }}\r\n                                slotProps={{\r\n                                  input: {\r\n                                    endAdornment: (\r\n                                      <span className=\"text-lg text-gray-500\">KG</span>\r\n                                    ),\r\n                                    inputProps: { min: 0, step: 0.001 },\r\n                                  }\r\n                                }}\r\n                              />\r\n                            </div>\r\n                          ) : (\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-lg text-gray-700 font-semibold\">Qty:</span>\r\n                              <div className=\"flex items-center space-x-2 bg-white rounded-xl border px-2 py-1\">\r\n                                <IconButton\r\n                                  onClick={() =>\r\n                                    handleDecreaseQuantity(item.productName)\r\n                                  }\r\n                                  size=\"medium\"\r\n                                  className=\"text-red-500 hover:bg-red-50\"\r\n                                  sx={{ width: 28, height: 28 }}\r\n                                >\r\n                                  <RemoveIcon style={{ fontSize: 18 }} />\r\n                                </IconButton>\r\n                                <span className=\"px-3 py-1 text-lg font-bold\">\r\n                                  {item.quantity || 1}\r\n                                </span>\r\n                                <IconButton\r\n                                  onClick={() =>\r\n                                    handleIncreaseQuantity(item.productName)\r\n                                  }\r\n                                  size=\"medium\"\r\n                                  className=\"text-green-500 hover:bg-green-50\"\r\n                                  sx={{ width: 28, height: 28 }}\r\n                                >\r\n                                  <AddIcon style={{ fontSize: 18 }} />\r\n                                </IconButton>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        <div className=\"text-right\">\r\n                          <div className=\"text-lg text-gray-700 font-semibold\">\r\n                            ₹{item.price} {item.weight_flag === 1 ? '/kg' : '/item'}\r\n                          </div>\r\n                          <div className=\"text-xl font-bold text-gray-900 mt-1\">\r\n                            ₹{(\r\n                              item.price *\r\n                              (item.weight_flag === 1\r\n                                ? item.weight\r\n                                : item.quantity)\r\n                            ).toFixed(2)}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Cart Summary at Bottom */}\r\n            <div className=\"mt-6 pt-4 border-t border-gray-300\">\r\n\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800 flex items-center\">\r\n                  Order\r\n                  {isCartLocked && (\r\n                    <span className=\"ml-2 px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded flex items-center\">\r\n                      <LockIcon style={{ fontSize: 12, marginRight: 2 }} />\r\n                      Locked\r\n                    </span>\r\n                  )}\r\n                </h3>\r\n                <div className=\"flex gap-2\">\r\n                  <Button\r\n                    onClick={handleClick}\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    sx={{ fontSize: 14, px: 1.5, py: 1, borderRadius: 2, minWidth: 70 }}\r\n                    startIcon={<FontAwesomeIcon icon={faShoppingCart} style={{ fontSize: 16 }} />}\r\n                  >\r\n                    Scan\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-2 mb-3\">\r\n                <div className=\"flex justify-between text-base\">\r\n                  <span className=\"text-gray-600 font-semibold\">Items:</span>\r\n                  <span className=\"font-bold\">{cartItems.length}</span>\r\n                </div>\r\n                <div className=\"flex justify-between text-base\">\r\n                  <span className=\"text-gray-600 font-semibold\">Subtotal:</span>\r\n                  <span className=\"font-bold\">₹{subtotal}</span>\r\n                </div>\r\n                <div className=\"border-t pt-2\">\r\n                  <div className=\"flex justify-between text-lg font-bold\">\r\n                    <span>Total:</span>\r\n                    <span className=\"text-blue-600\">\r\n                      {Number(totalAmount).toLocaleString(\"en-IN\", {\r\n                        style: \"currency\",\r\n                        currency: \"INR\",\r\n                        minimumFractionDigits: 0,\r\n                        maximumFractionDigits: 3,\r\n                      })}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column - Responsive Ads and Checkout */}\r\n          <div className=\"lg:col-span-2 flex flex-col gap-4\">\r\n            {/* Ads Section - Responsive */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-4 sm:p-6 flex-1 min-h-[300px] sm:min-h-[400px] lg:min-h-[500px]\">\r\n              <AdsSection />\r\n            </div>\r\n            \r\n            {/* Checkout Section - Compact */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-4\">\r\n              <div className=\"flex gap-3\">\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"success\"\r\n                  size=\"medium\"\r\n                  className=\"flex-1\"\r\n                  sx={{ fontSize: 14, py: 1.5, borderRadius: 2, fontWeight: 'bold' }}\r\n                  disabled={cartItems.length === 0}\r\n                >\r\n                  Checkout\r\n                </Button>\r\n                <Button\r\n                  onClick={handleClearCart}\r\n                  variant=\"outlined\"\r\n                  color=\"error\"\r\n                  size=\"medium\"\r\n                  className=\"flex-1\"\r\n                  sx={{ fontSize: 14, py: 1.5, borderRadius: 2, fontWeight: 'bold' }}\r\n                  disabled={cartItems.length === 0}\r\n                >\r\n                  Clear Cart\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search Dialog */}\r\n        <Dialog\r\n          open={openSearchDialog}\r\n          onClose={() => setOpenSearchDialog(false)}\r\n          fullWidth\r\n          maxWidth=\"sm\"\r\n        >\r\n          <DialogTitle>\r\n            Search Products\r\n            <IconButton\r\n              edge=\"end\"\r\n              color=\"inherit\"\r\n              onClick={() => setOpenSearchDialog(false)}\r\n              aria-label=\"close\"\r\n              sx={{ position: \"absolute\", right: 10, top: 10 }}\r\n            >\r\n              <DeleteIcon />\r\n            </IconButton>\r\n          </DialogTitle>\r\n          <DialogContent>\r\n            <TextField\r\n              fullWidth\r\n              variant=\"outlined\"\r\n              placeholder=\"Search for a product...\"\r\n              value={searchQuery}\r\n              onChange={handleInputChange}\r\n              sx={{ mb: 2, fontSize: 15, pr: 0 }}\r\n              InputProps={{\r\n                endAdornment: (\r\n                  searchQuery && (\r\n                    <IconButton\r\n                      aria-label=\"clear search\"\r\n                      size=\"small\"\r\n                      onClick={() => setSearchQuery(\"\")}\r\n                      edge=\"end\"\r\n                      sx={{ mr: 0.5 }}\r\n                    >\r\n                      <DeleteIcon fontSize=\"small\" />\r\n                    </IconButton>\r\n                  )\r\n                ),\r\n              }}\r\n            />\r\n            {displayedItems.length > 0 ? (\r\n              <List>\r\n                {displayedItems.map((item, index) => (\r\n                  <ListItem\r\n                    key={index}\r\n                    button\r\n                    onClick={() => {\r\n                      handleAddToCart(item);\r\n                      setOpenSearchDialog(false);\r\n                    }}\r\n                    sx={{\r\n                      display: \"flex\",\r\n                      justifyContent: \"space-between\",\r\n                      borderBottom: \"1px solid #ddd\",\r\n                      padding: \"8px\",\r\n                    }}\r\n                  >\r\n                    <Typography>{item.name}</Typography>\r\n                    <Typography\r\n                      sx={{\r\n                        fontWeight: \"semibold\",\r\n                        color: \"#000000\",\r\n                        mt: 2,\r\n                      }}\r\n                    >\r\n                      ₹{item.price}\r\n                    </Typography>\r\n                  </ListItem>\r\n                ))}\r\n              </List>\r\n            ) : (\r\n              <Typography\r\n                sx={{ textAlign: \"center\", mt: 2, color: \"gray\" }}\r\n              >\r\n                No products found.\r\n              </Typography>\r\n            )}\r\n          </DialogContent>\r\n        </Dialog>\r\n        {/* Add Product Dialog */}\r\n        <Dialog\r\n          open={openAddProductDialog}\r\n          onClose={handleCloseAddProductDialog}\r\n          fullWidth\r\n          maxWidth=\"sm\"\r\n        >\r\n          <DialogTitle>Add a Product</DialogTitle>\r\n          <DialogContent>\r\n            <Autocomplete\r\n              freeSolo\r\n              id=\"productName-autocomplete\"\r\n              options={productSuggestions}\r\n              getOptionLabel={(option) => option.productName || option.name || option || \"\"}\r\n              loading={productLoading}\r\n              value={newProductName}\r\n              inputValue={newProductName}\r\n              onInputChange={handleProductNameInput}\r\n              onChange={handleProductSuggestionSelect}\r\n              renderInput={(params) => (\r\n                <TextField\r\n                  {...params}\r\n                  label=\"Product Name\"\r\n                  variant=\"outlined\"\r\n                  sx={{ mt: 2, width: \"100%\" }}\r\n                  required\r\n                  InputProps={{\r\n                    ...params.InputProps,\r\n                    endAdornment: (\r\n                      <>\r\n                        {productLoading ? <CircularProgress color=\"inherit\" size={18} /> : null}\r\n                        {params.InputProps.endAdornment}\r\n                      </>\r\n                    ),\r\n                  }}\r\n                />\r\n              )}\r\n            />\r\n            <TextField\r\n              id=\"productPrice\"\r\n              variant=\"outlined\"\r\n              label=\"Product Price\"\r\n              value={newProductPrice}\r\n              onChange={(e) => setNewProductPrice(e.target.value)}\r\n              sx={{ mt: 2, width: \"100%\" }}\r\n              required\r\n            />\r\n          </DialogContent>\r\n          <DialogActions>\r\n            <Button onClick={handleCloseAddProductDialog}>Cancel</Button>\r\n            <Button\r\n              onClick={handleAddProductSubmit}\r\n              color=\"primary\"\r\n              variant=\"contained\"\r\n            >\r\n              Add Product\r\n            </Button>\r\n          </DialogActions>\r\n        </Dialog>\r\n\r\n        {/* Hidden canvas for motion detection */}\r\n        <canvas ref={canvasRef} style={{ display: \"none\" }} />\r\n\r\n        {/* Toast notifications with cart1.jsx styling */}\r\n        {showMotionToast && <div className=\"toast show -mt-10\">Motion Detected!</div>}\r\n        {showItemToast && <div className=\"toast show\">Item placed</div>}\r\n        \r\n        <style jsx>{`\r\n          .toast {\r\n            position: fixed;\r\n            top: 20px;\r\n            right: 20px;\r\n            background-color: #333;\r\n            color: white;\r\n            padding: 10px 20px;\r\n            border-radius: 5px;\r\n            font-size: 14px;\r\n            opacity: 0;\r\n            transform: translateY(-20px);\r\n            transition: all 0.3s ease;\r\n            z-index: 1000;\r\n          }\r\n          \r\n          .toast.show {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n          }\r\n        `}</style>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Cart;\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAAA;AAyBA;AACA;AAMA;AACA;AANA;AAJA;AAFA;AASA;AACA;AAPA;AAFA;AArBA;AAAA;AAAA;AAAA;AA4BA;AADA;AA3BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;AAkCA,MAAM,qBAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAM,OAAO,CAAC,EAAE,kBAAkB,EAAE;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,mDAAmD;IACnD,MAAM,0BAA0B,OAAO;QACrC,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,QAAQ;YAC7E,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,sBAAsB,EAAE;QAC1B,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,qDAAqD;IACrD,MAAM,yBAAyB,CAAC,OAAO,OAAO;QAC5C,kBAAkB;QAClB,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,wBAAwB;QAC1B,OAAO;YACL,sBAAsB,EAAE;YACxB,mBAAmB;QACrB;IACF;IAEA,+CAA+C;IAC/C,MAAM,gCAAgC,CAAC,OAAO;QAC5C,IAAI,SAAS,MAAM,WAAW,EAAE;YAC9B,kBAAkB,MAAM,WAAW;YACnC,2CAA2C;YAC3C,mBAAmB,MAAM,UAAU,IAAI,MAAM,KAAK,IAAI;QACxD,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,kBAAkB;YAClB,mBAAmB;QACrB;IACF;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE1C,kCAAkC;IAClC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAK,GAAG;QAAK,OAAO;QAAK,QAAQ;IAAI,IAAI,6BAA6B;IAC1G,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,+BAA+B;IAEtF,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK;YACxB,IAAI,KAAK,WAAW,KAAK,GAAG;gBAC1B,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,CAAC;YAC7C;YACA,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC;QAC/C,GAAG;IACL;IAEA,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,aAAa,oBAAoB;YACvC,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF;yBAAG;QAAC;QAAW;KAAmB;IAEhC,oBAAoB;IACpB,yBAAyB;IACzB,iCAAiC;IACjC,oCAAoC;IACpC,0BAA0B;IAC1B,iCAAiC;IAEjC,+CAA+C;IAC/C,YAAY;IACZ,yEAAyE;IACzE,4CAA4C;IAC5C,+CAA+C;IAC/C,sCAAsC;IACtC,0CAA0C;IAC1C,yBAAyB;IACzB,+BAA+B;IAC/B,4CAA4C;IAC5C,+CAA+C;IAC/C,sCAAsC;IACtC,wCAAwC;IAExC,4DAA4D;IAC5D,sCAAsC;IAEtC,0BAA0B;IAC1B,2DAA2D;IAC3D,sCAAsC;IACtC,oBAAoB;IACpB,YAAY;IAEZ,2DAA2D;IAE3D,gFAAgF;IAChF,iEAAiE;IAEjE,kCAAkC;IAClC,8CAA8C;IAC9C,gCAAgC;IAChC,+BAA+B;IAC/B,qBAAqB;IACrB,sBAAsB;IACtB,wBAAwB;IACxB,4BAA4B;IAC5B,eAAe;IAEf,8DAA8D;IAC9D,6DAA6D;IAC7D,wCAAwC;IACxC,gDAAgD;IAChD,gDAAgD;IAChD,0CAA0C;IAC1C,iCAAiC;IACjC,2CAA2C;IAC3C,6CAA6C;IAC7C,uBAAuB;IACvB,oDAAoD;IACpD,iDAAiD;IACjD,uDAAuD;IACvD,wCAAwC;IACxC,mDAAmD;IACnD,kDAAkD;IAClD,+DAA+D;IAC/D,mCAAmC;IACnC,2CAA2C;IAC3C,0BAA0B;IAC1B,gBAAgB;IAChB,cAAc;IACd,YAAY;IAEZ,8CAA8C;IAC9C,oCAAoC;IACpC,WAAW;IACX,wBAAwB;IACxB,qEAAqE;IACrE,QAAQ;IACR,OAAO;IAEP,6DAA6D;IAC7D,0CAA0C;IAC1C,aAAa;IAGd,OAAO;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,WAAW,IAAI,yBAAyB;YAC9C,MAAM,gBAAgB,OAAO,yBAAyB;YACtD,MAAM,uBAAuB,GAAG,+CAA+C;YAC/E,IAAI,eAAe;YACnB,IAAI,mBAAmB;YAEzB,KAAK;YACL,MAAM;uDAAuB;oBAC3B,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,MAAM,MAAM,IAAI,eAAe,CAAC;wBAChC,6BAA6B;wBAC7B,MAAM,QAAQ,IAAI,OAAO,KAAK;wBAC9B,MAAM,GAAG,GAAG;wBACZ,MAAM,MAAM;mEAAG;gCACb,MAAM,SAAS,UAAU,OAAO;gCAChC,MAAM,MAAM,OAAO,UAAU,CAAC;gCAC9B,OAAO,KAAK,GAAG,MAAM,KAAK;gCAC1B,OAAO,MAAM,GAAG,MAAM,MAAM;gCAE5B,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;gCAC/C,IAAI,SAAS,CAAC,OAAO,GAAG;gCAExB;gCACA,IAAI,eAAe,yBAAyB,GAAG;oCAC7C,IAAI,eAAe,CAAC;oCACpB;gCACF;gCAEA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gCAE5C,MAAM,mBAAmB,IAAI,YAAY,CAAC,MAAM,MAAM,OAAO;gCAC7D,MAAM,kBAAkB,YAAY;gCAEpC,IAAI,kBAAkB;oCACpB,MAAM,cAAc,aAClB,kBACA,iBACA,OACA,QACA,UACA;oCAGF,MAAM,oBAAoB,YAAY,MAAM,GAAG;oCAC/C,IAAI,qBAAqB,CAAC,YAAY,OAAO,EAAE;wCAC7C,mBAAmB;wCACnB,kBAAkB,OAAO,GAAG;wCAC5B,YAAY,OAAO,GAAG;wCAEtB,sCAAsC;wCACtC;wCAEA;mFAAW;gDACT,mBAAmB;gDACnB,YAAY,OAAO,GAAG;4CACxB;kFAAG,MAAM,kCAAkC;oCAC7C;gCACF;gCAEA,mBAAmB;gCACnB,IAAI,eAAe,CAAC;4BACtB;;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uCAAuC;oBACvD;gBACF;;YAEA,MAAM,WAAW,YAAY,sBAAsB,MAAM,kCAAkC;YAC3F;kCAAO,IAAM,cAAc;;QAC7B;yBAAG;QAAC;KAAI;IAEN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,YAAY;gBACd;gBACA,cAAc,QAAQ,oCAAoC;YAC5D;QACF;yBAAG;QAAC;KAAW;IAEf,MAAM,cAAc,CAAC;QACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QAChC,MAAM,WAAW,IAAI,kBAAkB,QAAQ;QAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE;YACxE,QAAQ,CAAC,IAAI,EAAE,GAAG;QACpB;QACA,OAAO;IACT;IAEA,MAAM,eAAe,CAAC,UAAU,aAAa,OAAO,QAAQ,UAAU;QACpE,MAAM,cAAc,EAAE;QACtB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,UAAU,KAAK,SAAU;YACrD,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,UAAU,KAAK,SAAU;gBACpD,IAAI,WAAW;gBACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,IAAK;oBACrC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,UAAU,IAAK;wBACrC,MAAM,QAAQ,IAAI,QAAQ;wBAC1B,IAAI,QAAQ,SAAS,MAAM,EAAE;4BAC3B,YAAY,KAAK,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM;wBAC3D;oBACF;gBACF;gBACA,IAAI,WAAW,WAAW;oBACxB,YAAY,IAAI,CAAC;wBAAE;wBAAG;oBAAE;gBAC1B;YACF;QACF;QACA,OAAO;IACT;IAGA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,wCAAmC;gBACjC,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,aAAa,aAAa,KAAK,KAAK,CAAC,cAAc,EAAE;YACvD;QACF;yBAAG,EAAE;IACL,MAAM,WAAW,UACd,MAAM,CAAC,CAAC,KAAK;QACZ,IAAI,KAAK,WAAW,KAAK,GAAG;YAC1B,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,IAAI,CAAC;QAC7C;QACA,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,KAAK,QAAQ,IAAI,CAAC;IAC/C,GAAG,GACF,OAAO,CAAC;IAGX,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW;IAE1C,MAAM,uBAAuB,OAAO,OAAO;QACzC,IAAI;YACF,MAAM,gBAAgB,mBAAmB,GAAG,CAAC;YAC7C,MAAM,gBAAgB,gBAAgB,eAAe;YACrD,MAAM,gBAAgB,MAAM,MAC1B,CAAC,KAAK,EAAE,cAAc,aAAa,EAAE,mBACnC,iBACC;YAGL,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,eAAe,EAAE,EAAE,cAAc,UAAU,EAAE;YAE9E;YAEA,MAAM,YAAY,MAAM,cAAc,IAAI;YAC1C,MAAM,WAAW,gBACb,UAAU,UAAU,GACpB,UAAU,QAAQ;YAEtB,aAAa,CAAC;gBACZ,MAAM,mBAAmB,cAAc,GAAG,CAAC,CAAC,MAAM;oBAChD,IAAI,MAAM,OAAO;wBACf,MAAM,cAAc;4BAClB,GAAG,IAAI;4BACP,iBAAiB;4BACjB,aAAa;4BACb,OAAO;4BACP,aAAa,gBAAgB,IAAI;wBACnC;wBAEA,IAAI,KAAK,WAAW,KAAK,CAAC,gBAAgB,IAAI,CAAC,GAAG;4BAChD,IAAI,eAAe;gCACjB,OAAO,YAAY,QAAQ;gCAC3B,YAAY,MAAM,GAAG;4BACvB,OAAO;gCACL,OAAO,YAAY,MAAM;gCACzB,YAAY,QAAQ,GAAG;4BACzB;wBACF;wBAEA,OAAO;oBACT;oBACA,OAAO;gBACT;gBAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAGA,GAAG;IACH,wDAAwD;IACxD,UAAU;IACV,4DAA4D;IAE5D,uEAAuE;IACvE,0BAA0B;IAC1B,yEAAyE;IACzE,QAAQ;IACR,0CAA0C;IAE1C,uCAAuC;IACvC,yDAAyD;IAEzD,yEAAyE;IAEzE,kGAAkG;IAClG,wBAAwB;IACxB,wBAAwB;IACxB,UAAU;IAEV,iCAAiC;IACjC,qFAAqF;IACrF,QAAQ;IAER,iDAAiD;IACjD,yBAAyB;IACzB,wDAAwD;IAExD,8EAA8E;IAC9E,gCAAgC;IAChC,mDAAmD;IACnD,QAAQ;IACR,sDAAsD;IACtD,wCAAwC;IAExC,4BAA4B;IAC5B,2CAA2C;IAE3C,+CAA+C;IAC/C,oEAAoE;IACpE,8CAA8C;IAC9C,iDAAiD;IACjD,qEAAqE;IACrE,2EAA2E;IAC3E,6CAA6C;IAC7C,oGAAoG;IACpG,aAAa;IAEb,mCAAmC;IACnC,yEAAyE;IACzE,YAAY;IAEZ,wDAAwD;IACxD,mFAAmF;IAEnF,qFAAqF;IAErF,mBAAmB;IACnB,yBAAyB;IACzB,gDAAgD;IAChD,0DAA0D;IAC1D,kHAAkH;IAClH,aAAa;IACb,WAAW;IACX,SAAS;IAET,kDAAkD;IAClD,wCAAwC;IACxC,gEAAgE;IAChE,0CAA0C;IAC1C,WAAW;IAEX,mDAAmD;IACnD,4CAA4C;IAC5C,gEAAgE;IAChE,+DAA+D;IAC/D,aAAa;IAEb,wCAAwC;IACxC,sEAAsE;IACtE,6CAA6C;IAC7C,sDAAsD;IACtD,iCAAiC;IACjC,4EAA4E;IAC5E,iBAAiB;IACjB,qBAAqB;IACrB,sDAAsD;IACtD,iCAAiC;IACjC,kFAAkF;IAClF,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,4CAA4C;IAC5C,YAAY;IACZ,YAAY;IAEZ,6EAA6E;IAC7E,iCAAiC;IACjC,UAAU;IAEV,yBAAyB;IACzB,sBAAsB;IACtB,iEAAiE;IACjE,MAAM;IACN,UAAU;IAKV,wDAAwD;IACxD,UAAU;IACV,4DAA4D;IAE5D,uEAAuE;IACvE,0BAA0B;IAC1B,yEAAyE;IACzE,QAAQ;IACR,0CAA0C;IAE1C,uCAAuC;IACvC,yDAAyD;IAEzD,yEAAyE;IAEzE,kGAAkG;IAClG,wBAAwB;IACxB,wBAAwB;IACxB,UAAU;IAEV,iCAAiC;IACjC,qFAAqF;IACrF,QAAQ;IAER,iDAAiD;IACjD,yBAAyB;IACzB,wDAAwD;IAExD,8EAA8E;IAC9E,gCAAgC;IAChC,mDAAmD;IACnD,QAAQ;IACR,sDAAsD;IACtD,wCAAwC;IAExC,4BAA4B;IAC5B,2CAA2C;IAE3C,iCAAiC;IACjC,oEAAoE;IACpE,8CAA8C;IAC9C,iDAAiD;IACjD,qEAAqE;IACrE,2EAA2E;IAC3E,6CAA6C;IAC7C,oGAAoG;IACpG,aAAa;IAEb,mCAAmC;IACnC,yEAAyE;IACzE,YAAY;IAEZ,wDAAwD;IACxD,mFAAmF;IAEnF,qFAAqF;IAErF,mBAAmB;IACnB,yBAAyB;IACzB,gDAAgD;IAChD,0DAA0D;IAC1D,kHAAkH;IAClH,6DAA6D;IAC7D,aAAa;IACb,WAAW;IACX,SAAS;IAET,2BAA2B;IAC3B,wCAAwC;IACxC,qCAAqC;IACrC,6EAA6E;IAE7E,kEAAkE;IAClE,6DAA6D;IAC7D,mDAAmD;IACnD,iFAAiF;IACjF,aAAa;IAEb,8BAA8B;IAC9B,gFAAgF;IAChF,6CAA6C;IAC7C,uBAAuB;IACvB,4BAA4B;IAC5B,4CAA4C;IAC5C,iBAAiB;IACjB,qBAAqB;IACrB,uBAAuB;IACvB,4BAA4B;IAC5B,gDAAgD;IAChD,iBAAiB;IACjB,cAAc;IACd,YAAY;IACZ,0BAA0B;IAC1B,YAAY;IAEZ,8CAA8C;IAC9C,sEAAsE;IACtE,6EAA6E;IAC7E,iCAAiC;IACjC,UAAU;IAEV,yBAAyB;IACzB,sBAAsB;IACtB,iEAAiE;IACjE,MAAM;IACN,UAAU;IAGV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACtC,IAAI;gBACF,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,sBAAsB;oBAAE,WAAW;gBAAI;gBAElD,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;gBAChE;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,SAAS,MAAM;gBAE/B,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,mCAAmC;oBAAE,WAAW;gBAAI;gBAE/D,MAAM,kBAAkB,MAAM,MAAM,qCAAqC;oBACvE,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,gBAAgB,UAAU,EAAE;gBAC5E;gBAEA,MAAM,OAAO,MAAM,gBAAgB,IAAI;gBACvC,QAAQ,GAAG,CAAC;gBACZ,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;gBAE5C,MAAM,iBAAiB,MAAM,MAAM;gBACnC,IAAI,CAAC,eAAe,EAAE,EAAE;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,MAAM,SAAS,WAAW,MAAM;gBAEhC,WAAW;gBACX,QAAQ,GAAG,CAAC,cAAc;gBAE1B,0BAA0B;gBAC1B,MAAM,OAAO,MAAM,OAAO,CAAC,YAAY,WAAW;oBAAC;iBAAS;gBAE5D,uDAAuD;gBACvD,MAAM,gBAAgB,KAAK,MAAM;2EAAC,CAAC,KAAK;wBACtC,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI;wBACrC,OAAO;oBACT;0EAAG,CAAC;gBAEJ,MAAM,eAAe,MAAM,QAAQ,GAAG,CACpC,KAAK,GAAG;6DAAC,OAAO,aAAa;wBAC3B,MAAM,gBAAgB,mBAAmB,GAAG,CAAC;wBAC7C,MAAM,gBAAgB,gBAAgB,eAAe;wBACrD,MAAM,gBAAgB,MAAM,MAC1B,CAAC,KAAK,EAAE,cAAc,aAAa,EAAE,mBAAmB,cAAc;wBAGxE,IAAI,CAAC,cAAc,EAAE,EAAE;4BACrB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,aAAa;wBAC5D;wBAEA,MAAM,YAAY,MAAM,cAAc,IAAI;wBAC1C,MAAM,QAAQ,gBAAgB,UAAU,UAAU,GAAG,UAAU,QAAQ;wBAEvE,MAAM,eAAe,QAAQ,CAAC,MAAM,CAAC,MAAM;kFAAC,CAAC,MAAQ,QAAQ;;wBAE7D,OAAO;4BACL;4BACA,aAAa,gBAAgB,IAAI;4BACjC,cAAc;gCAAC;mCAAgB;6BAAa;4BAC5C,GAAI,gBACA;gCAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,aAAa,CAAC,YAAY;gCAAE,OAAO,SAAS;4BAAE,IACxE;gCAAE,UAAU,aAAa,CAAC,YAAY;gCAAE,OAAO,SAAS;4BAAE,CAAC;4BAE/D,aAAa;wBACf;oBACF;;gBAGF,qDAAqD;gBACrD;6DAAa,CAAC;wBACZ,IAAI,gBAAgB,WAAW;4BAC7B,uFAAuF;4BACvF,MAAM,mBAAmB;mCAAI;6BAAc;4BAE3C,aAAa,OAAO;yEAAC,CAAC;oCACpB,MAAM,oBAAoB,iBAAiB,SAAS;mGAClD,CAAC,OAAS,KAAK,WAAW,KAAK,QAAQ,WAAW;;oCAGpD,IAAI,sBAAsB,CAAC,GAAG;wCAC5B,qDAAqD;wCACrD,iBAAiB,IAAI,CAAC;4CACpB,GAAG,OAAO;4CACV,aAAa;4CACb,gBAAgB;wCAClB;oCACF,OAAO,IAAI,WAAW;wCACpB,0DAA0D;wCAC1D,MAAM,eAAe,gBAAgB,CAAC,kBAAkB;wCACxD,IAAI,QAAQ,WAAW,KAAK,GAAG;4CAC7B,gBAAgB,CAAC,kBAAkB,GAAG;gDACpC,GAAG,YAAY;gDACf,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC;4CAC3D;wCACF,OAAO;4CACL,gBAAgB,CAAC,kBAAkB,GAAG;gDACpC,GAAG,YAAY;gDACf,UAAU,CAAC,aAAa,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC;4CACjE;wCACF;oCACF;gCACA,gFAAgF;gCAClF;;4BAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;4BACjD,OAAO;wBACT,OAAO;4BACL,gEAAgE;4BAChE,MAAM,cAAc,cAAc,MAAM;qFAAC,CAAA,OAAQ,CAAC,KAAK,WAAW;;4BAElE,MAAM,uBAAuB,MAAM,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG;8FAAC,CAAA,OAAQ,KAAK,WAAW;+FACtF,GAAG;8FAAC,CAAA;oCACH,MAAM,OAAO,aAAa,IAAI;2GAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;;oCAC5D,MAAM,eAAe,cAAc,IAAI;mHACrC,CAAA,WAAY,SAAS,WAAW,KAAK,eAAe,SAAS,WAAW;;oCAG1E,IAAI,cAAc;wCAChB,IAAI,KAAK,WAAW,KAAK,GAAG;4CAC1B,OAAO;gDACL,GAAG,IAAI;gDACP,QAAQ,aAAa,MAAM;4CAC7B;wCACF,OAAO;4CACL,OAAO;gDACL,GAAG,IAAI;gDACP,UAAU,aAAa,QAAQ;4CACjC;wCACF;oCACF;oCACA,OAAO;gCACT;;4BAEF,MAAM,mBAAmB;mCAAI;mCAAgB;6BAAqB;4BAClE,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;4BACjD,OAAO;wBACT;oBACF;;gBAEA,UAAU;YACZ,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;gDAAG;QAAC;QAAc;KAAU;IAE9B,+BAA+B;IAC/B,MAAM,aAAa,CAAC;QAClB,aAAa,CAAC;YACZ,MAAM,mBAAmB;mBAAI;aAAc;YAC3C,MAAM,oBAAoB,iBAAiB,SAAS,CAClD,CAAC,OAAS,KAAK,WAAW,KAAK,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW;YAGzE,IAAI,sBAAsB,CAAC,GAAG;gBAC5B,wCAAwC;gBACxC,iBAAiB,IAAI,CAAC;oBACpB,GAAG,OAAO;oBACV,aAAa;gBACf;YACF,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,eAAe,gBAAgB,CAAC,kBAAkB;gBACxD,IAAI,QAAQ,WAAW,KAAK,GAAG;oBAC7B,gBAAgB,CAAC,kBAAkB,GAAG;wBACpC,GAAG,YAAY;wBACf,QAAQ,CAAC,aAAa,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC;oBAC3D;gBACF,OAAO;oBACL,gBAAgB,CAAC,kBAAkB,GAAG;wBACpC,GAAG,YAAY;wBACf,UAAU,CAAC,aAAa,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC;oBACjE;gBACF;YACF;YAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IACE,MAAM,yBAAyB,CAAC;QAC9B,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,iBAAiB,EAAE;QACrE,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC,OACtC,KAAK,WAAW,KAAK,cACjB;gBAAE,GAAG,IAAI;gBAAE,UAAU,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;YAAE,IAC9C;QAEN,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACjD,aAAa;IACf;IAEA,MAAM,yBAAyB,CAAC;QAC9B,aAAa,CAAC;YACZ,MAAM,mBAAmB,UACtB,GAAG,CAAC,CAAC;gBACJ,IAAI,KAAK,WAAW,KAAK,aAAa;oBACpC,MAAM,cAAc,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI;oBAC3C,OAAO,cAAc,IAAI;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAY,IAAI;gBAChE;gBACA,OAAO;YACT,GACC,MAAM,CAAC;YAEV,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa,CAAC;YACZ,MAAM,gBAAgB,cAAc,SAAS,CAC3C,CAAC,OAAS,KAAK,WAAW,KAAK;YAEjC,IAAI,kBAAkB,CAAC,GAAG,OAAO;YAEjC,MAAM,eAAe,aAAa,CAAC,cAAc;YAEjD,qDAAqD;YACrD,IAAI,gBAAgB,aAAa,QAAQ,EAAE;gBACzC,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT;YAEA,MAAM,mBAAmB;mBACpB,cAAc,KAAK,CAAC,GAAG;mBACvB,cAAc,KAAK,CAAC,gBAAgB;aACxC;YACD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YAEjD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,iDAAiD;QACjD,MAAM,cAAc,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACrD,IAAI,CAAC,aAAa;YAChB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,MAAM,gBAAgB,mBAAmB,GAAG,CAAC;QAC7C,IAAI,gBAAgB;QAEpB,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,gBAAgB,KAAK,MAAM,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,IAAI;QAEJ,IAAI,eAAe;YACjB,mBAAmB;mBACd;gBACH;oBACE,GAAG,IAAI;oBACP;oBACA,aAAa;oBACb,cAAc;wBAAC;qBAAY;oBAC3B,QAAQ;oBACR,OAAO,KAAK,UAAU,IAAI,KAAK,KAAK,IAAI;gBAC1C;aACD;YACD,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,EAAE,YAAY,QAAQ,CAAC;QACjE,OAAO;YACL,MAAM,oBAAoB,UAAU,SAAS,CAC3C,CAAC,WACC,SAAS,WAAW,KAAK,eAAe,CAAC,SAAS,WAAW;YAGjE,IAAI,oBAAoB,CAAC,GAAG;gBAC1B,mBAAmB,UAAU,GAAG,CAAC,CAAC,UAAU;oBAC1C,IAAI,UAAU,mBAAmB;wBAC/B,MAAM,cAAc,CAAC,SAAS,QAAQ,IAAI,CAAC,IAAI;wBAC/C,sJAAA,CAAA,QAAK,CAAC,IAAI,CACR,CAAC,QAAQ,EAAE,YAAY,aAAa,EAAE,aAAa;wBAErD,OAAO;4BACL,GAAG,QAAQ;4BACX,UAAU;4BACV,OAAO,KAAK,KAAK,IAAI;wBACvB;oBACF;oBACA,OAAO;gBACT;YACF,OAAO;gBACL,mBAAmB;uBACd;oBACH;wBACE,GAAG,IAAI;wBACP;wBACA,aAAa;wBACb,cAAc;4BAAC;yBAAY;wBAC3B,UAAU;wBACV,OAAO,KAAK,KAAK,IAAI;oBACvB;iBACD;gBACD,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,YAAY,QAAQ,CAAC;YAC3C;QACF;QAEA,aAAa;QACb,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD;IAEA,MAAM,6BAA6B;QACjC,wBAAwB;IAC1B;IAEA,MAAM,8BAA8B;QAClC,wBAAwB;QACxB,kBAAkB;QAClB,mBAAmB;QACnB,sBAAsB;QACtB,uBAAuB;IACzB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;YACvC,SAAS;YACT;QACF;QAEA,MAAM,aAAa;YACjB,KAAK,KAAK,GAAG,GAAG,QAAQ;YACxB,aAAa;YACb,OAAO,WAAW;YAClB,UAAU;YACV,WAAW;QACb;QAEA,SAAS;eAAI;YAAO;SAAW;QAC/B,gBAAgB;QAChB;IACF;IAGA,sBAAsB;IACtB,MAAM,cAAc;QAClB;QACA,WAAW,IAAM,WAAW,QAAQ;IACtC;IAEA,MAAM,kBAAkB;QACtB,aAAa,EAAE,GAAG,yBAAyB;QAC3C,aAAa,UAAU,CAAC,cAAc,2BAA2B;QACjE,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B,2BAA2B;IAC1E;IAEA,MAAM,mBAAmB;QACvB,MAAM,eAAe,CAAC;QACtB,gBAAgB;QAEhB,IAAI,cAAc;YAChB,sEAAsE;YACtE,aAAa;YACb,aAAa,CAAC;gBACZ,MAAM,mBAAmB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAClD,GAAG,IAAI;wBACP,UAAU;wBACV,UAAU,KAAK,GAAG;oBACpB,CAAC;gBACD,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;YACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,sCAAsC;YACtC,aAAa,CAAC;gBACZ,MAAM,mBAAmB,cAAc,GAAG,CAAC,CAAA;oBACzC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,iBAAiB,GAAG;oBACnD,OAAO;gBACT;gBACA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;YACA,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,aAAa,CAAC;QACpB,aAAa;QAEb,IAAI,YAAY;YACd,4CAA4C;YAC5C,gBAAgB;YAChB,aAAa,CAAC;gBACZ,MAAM,mBAAmB,cAAc,GAAG,CAAC,CAAA;oBACzC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,iBAAiB,GAAG;oBACnD,OAAO;gBACT;gBACA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,OAAO;YACT;YACA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,kBAAkB,EAAE,mBAAmB,QAAQ;YAElD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,mBAAmB,KAAK,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC3C,MAAM,KAAK,WAAW;oBACtB,OAAO,KAAK,KAAK;gBACnB,CAAC;YAED,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,IAAI,MAAM,IAAI,OAAO,IAAI;YACvB,kBAAkB,EAAE;YACpB;QACF;QACA,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QACf,aAAa;IACf;IAEA,MAAM,qBAAqB,CAAC,aAAa;QACvC,aAAa,CAAC;YACZ,MAAM,eAAe,UAAU,GAAG,CAAC,CAAC;gBAClC,IAAI,KAAK,WAAW,KAAK,aAAa;oBACpC,MAAM,SAAS,KAAK,GAAG,CACrB,GACA,KAAK,KAAK,CAAC,WAAW,aAAa,KAAK,QAAQ;oBAElD,OAAO;wBACL,GAAG,IAAI;wBACP,QAAQ;oBACV;gBACF;gBACA,OAAO;YACT;YAEA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACjD,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;sDAAc;;8BACb,6LAAC;8DAAc;;sCAEb,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;;8DACZ,6LAAC,uKAAA,CAAA,kBAAe;oDAAC,MAAM,2KAAA,CAAA,iBAAc;oDAAE,WAAU;;;;;;gDAAgC;8DACvE,6LAAC;8FAAe;;wDAA2C;wDAAE,UAAU,MAAM;wDAAC;;;;;;;gDACvF,8BACC,6LAAC;8FAAe;;sEACd,6LAAC,4JAAA,CAAA,UAAQ;4DAAC,OAAO;gEAAE,UAAU;gEAAI,aAAa;4DAAE;;;;;;wDAAK;;;;;;;;;;;;;sDAK3D,6LAAC;sFAAc;;8DACb,6LAAC,gKAAA,CAAA,UAAU;oDACT,SAAS,IAAM,oBAAoB;oDACnC,OAAM;oDACN,MAAK;oDACL,IAAI;wDAAE,OAAO;wDAAI,QAAQ;wDAAI,cAAc;wDAAG,QAAQ;wDAAqB,YAAY;wDAAW,IAAI;oDAAE;8DAExG,cAAA,6LAAC,sKAAA,CAAA,UAAkB;wDAAC,OAAO;4DAAE,UAAU;wDAAG;;;;;;;;;;;8DAE5C,6LAAC,gKAAA,CAAA,UAAU;oDACT,SAAS,IAAM,wBAAwB;oDACvC,OAAM;oDACN,MAAK;oDACL,IAAI;wDAAE,OAAO;wDAAI,QAAQ;wDAAI,cAAc;wDAAG,QAAQ;wDAAqB,YAAY;wDAAW,IAAI;oDAAE;8DAExG,cAAA,6LAAC,mKAAA,CAAA,UAAe;wDAAC,OAAO;4DAAE,UAAU;wDAAG;;;;;;;;;;;8DAEzC,6LAAC,gKAAA,CAAA,UAAU;oDACT,SAAS;oDACT,OAAO,eAAe,YAAY;oDAClC,MAAK;oDACL,IAAI;wDACF,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,eAAe,sBAAsB;wDAC7C,YAAY,eAAe,YAAY;wDACvC,IAAI;oDACN;oDACA,OAAO,eAAe,qCAAqC;8DAE1D,6BAAe,6LAAC,4JAAA,CAAA,UAAQ;wDAAC,OAAO;4DAAE,UAAU;wDAAG;;;;;6EAAQ,6LAAC,gKAAA,CAAA,UAAY;wDAAC,OAAO;4DAAE,UAAU;wDAAG;;;;;;;;;;;8DAE9F,6LAAC,gKAAA,CAAA,UAAU;oDACT,SAAS;oDACT,OAAM;oDACN,MAAK;oDACL,IAAI;wDAAE,OAAO;wDAAI,QAAQ;wDAAI,cAAc;wDAAG,QAAQ;wDAAqB,YAAY;oDAAU;8DAEjG,cAAA,6LAAC,8JAAA,CAAA,UAAU;wDAAC,OAAO;4DAAE,UAAU;wDAAG;;;;;;;;;;;;;;;;;;;;;;;gCAKvC,UAAU,MAAM,KAAK,mBACpB,6LAAC;8EAAc;;sDACb,6LAAC,uKAAA,CAAA,kBAAe;4CAAC,MAAM,2KAAA,CAAA,iBAAc;4CAAE,WAAU;;;;;;sDACjD,6LAAC;sFAAa;sDAAyB;;;;;;sDACvC,6LAAC;sFAAY;sDAA2B;;;;;;sDACxC,6LAAC,6LAAA,CAAA,SAAM;4CACL,SAAS,IAAM,oBAAoB;4CACnC,SAAQ;4CACR,IAAI;gDAAE,UAAU;gDAAI,IAAI;gDAAG,IAAI;gDAAG,cAAc;4CAAE;4CAClD,yBAAW,6LAAC,sKAAA,CAAA,UAAkB;gDAAC,OAAO;oDAAE,UAAU;gDAAG;;;;;;sDACtD;;;;;;;;;;;;gCAOJ,UAAU,MAAM,GAAG,mBAClB,6LAAC;8EAAc;8CACb,cAAA,6LAAC;kFAAc;kDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gDAGC,OAAO;oDAAE,WAAW;gDAAG;0FADb;;kEAGV,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;;kFACb,6LAAC,4MAAA,CAAA,cAAW;wEAAC,MAAK;wEAAS,WAAU;kFACnC,cAAA,6LAAC,6LAAA,CAAA,SAAM;4EACL,OAAO,KAAK,eAAe,IAAI,KAAK,WAAW,IAAI;4EACnD,UAAU,CAAC,IACT,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAE5C,YAAY;4EACZ,WAAU;4EACV,IAAI;gFAAE,UAAU;gFAAI,WAAW;4EAAG;sFAEjC,KAAK,YAAY,IAChB,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBAClC,6LAAC,mMAAA,CAAA,WAAQ;oFAAS,OAAO;oFAAa,OAAO;wFAAE,UAAU;wFAAI,WAAW;oFAAG;8FACxE;mFADY;;;;;;;;;;;;;;;oEAMtB,gBAAgB,KAAK,QAAQ,kBAC5B,6LAAC;kHAAe;;0FACd,6LAAC,4JAAA,CAAA,UAAQ;gFAAC,OAAO;oFAAE,UAAU;oFAAI,aAAa;gFAAE;;;;;;4EAAK;;;;;;;oEAIxD,gBAAgB,CAAC,KAAK,QAAQ,kBAC7B,6LAAC;kHAAe;kFAA2F;;;;;;;;;;;;0EAK/G,6LAAC,gKAAA,CAAA,UAAU;gEACT,SAAS,IAAM,gBAAgB,KAAK,WAAW;gEAC/C,MAAK;gEACL,WAAU;gEACV,IAAI;oEAAE,OAAO;oEAAI,QAAQ;gEAAG;gEAC5B,UAAU,gBAAgB,KAAK,QAAQ;gEACvC,OAAO,gBAAgB,KAAK,QAAQ,GAAG,8BAA8B;0EAErE,cAAA,6LAAC,8JAAA,CAAA,UAAU;oEAAC,OAAO;wEAAE,UAAU;oEAAG;;;;;;;;;;;;;;;;;kEAItC,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;0EACZ,KAAK,WAAW,KAAK,kBACpB,6LAAC;8GAAc;;sFACb,6LAAC;sHAAe;sFAAsC;;;;;;sFACtD,6LAAC,sMAAA,CAAA,YAAS;4EACR,MAAK;4EACL,OAAO,WAAW,KAAK,MAAM,IAAI;4EACjC,UAAU,CAAC,IACT,mBACE,KAAK,WAAW,EAChB,EAAE,MAAM,CAAC,KAAK;4EAGlB,MAAK;4EACL,WAAU;4EACV,YAAY;gFAAE,OAAO;oFAAE,UAAU;oFAAI,QAAQ;gFAAG;4EAAE;4EAClD,WAAW;gFACT,OAAO;oFACL,4BACE,6LAAC;kIAAe;kGAAwB;;;;;;oFAE1C,YAAY;wFAAE,KAAK;wFAAG,MAAM;oFAAM;gFACpC;4EACF;;;;;;;;;;;yFAIJ,6LAAC;8GAAc;;sFACb,6LAAC;sHAAe;sFAAsC;;;;;;sFACtD,6LAAC;sHAAc;;8FACb,6LAAC,gKAAA,CAAA,UAAU;oFACT,SAAS,IACP,uBAAuB,KAAK,WAAW;oFAEzC,MAAK;oFACL,WAAU;oFACV,IAAI;wFAAE,OAAO;wFAAI,QAAQ;oFAAG;8FAE5B,cAAA,6LAAC,8JAAA,CAAA,UAAU;wFAAC,OAAO;4FAAE,UAAU;wFAAG;;;;;;;;;;;8FAEpC,6LAAC;8HAAe;8FACb,KAAK,QAAQ,IAAI;;;;;;8FAEpB,6LAAC,gKAAA,CAAA,UAAU;oFACT,SAAS,IACP,uBAAuB,KAAK,WAAW;oFAEzC,MAAK;oFACL,WAAU;oFACV,IAAI;wFAAE,OAAO;wFAAI,QAAQ;oFAAG;8FAE5B,cAAA,6LAAC,2JAAA,CAAA,UAAO;wFAAC,OAAO;4FAAE,UAAU;wFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0EAMzC,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;;4EAAsC;4EACjD,KAAK,KAAK;4EAAC;4EAAE,KAAK,WAAW,KAAK,IAAI,QAAQ;;;;;;;kFAElD,6LAAC;kHAAc;;4EAAuC;4EAClD,CACA,KAAK,KAAK,GACV,CAAC,KAAK,WAAW,KAAK,IAClB,KAAK,MAAM,GACX,KAAK,QAAQ,CACnB,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;+CApHX;;;;;;;;;;;;;;;8CA+Hf,6LAAC;8EAAc;;sDAEb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAa;;wDAAwD;wDAEnE,8BACC,6LAAC;sGAAe;;8EACd,6LAAC,4JAAA,CAAA,UAAQ;oEAAC,OAAO;wEAAE,UAAU;wEAAI,aAAa;oEAAE;;;;;;gEAAK;;;;;;;;;;;;;8DAK3D,6LAAC;8FAAc;8DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM;wDACL,SAAS;wDACT,SAAQ;wDACR,OAAM;wDACN,IAAI;4DAAE,UAAU;4DAAI,IAAI;4DAAK,IAAI;4DAAG,cAAc;4DAAG,UAAU;wDAAG;wDAClE,yBAAW,6LAAC,uKAAA,CAAA,kBAAe;4DAAC,MAAM,2KAAA,CAAA,iBAAc;4DAAE,OAAO;gEAAE,UAAU;4DAAG;;;;;;kEACzE;;;;;;;;;;;;;;;;;sDAML,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAe;sEAA8B;;;;;;sEAC9C,6LAAC;sGAAe;sEAAa,UAAU,MAAM;;;;;;;;;;;;8DAE/C,6LAAC;8FAAc;;sEACb,6LAAC;sGAAe;sEAA8B;;;;;;sEAC9C,6LAAC;sGAAe;;gEAAY;gEAAE;;;;;;;;;;;;;8DAEhC,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;;0EACb,6LAAC;;0EAAK;;;;;;0EACN,6LAAC;0GAAe;0EACb,OAAO,aAAa,cAAc,CAAC,SAAS;oEAC3C,OAAO;oEACP,UAAU;oEACV,uBAAuB;oEACvB,uBAAuB;gEACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWZ,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAc;8CACb,cAAA,6LAAC,+IAAA,CAAA,UAAU;;;;;;;;;;8CAIb,6LAAC;8EAAc;8CACb,cAAA,6LAAC;kFAAc;;0DACb,6LAAC,6LAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,IAAI;oDAAE,UAAU;oDAAI,IAAI;oDAAK,cAAc;oDAAG,YAAY;gDAAO;gDACjE,UAAU,UAAU,MAAM,KAAK;0DAChC;;;;;;0DAGD,6LAAC,6LAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,OAAM;gDACN,MAAK;gDACL,WAAU;gDACV,IAAI;oDAAE,UAAU;oDAAI,IAAI;oDAAK,cAAc;oDAAG,YAAY;gDAAO;gDACjE,UAAU,UAAU,MAAM,KAAK;0DAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAST,6LAAC,6LAAA,CAAA,SAAM;oBACL,MAAM;oBACN,SAAS,IAAM,oBAAoB;oBACnC,SAAS;oBACT,UAAS;;sCAET,6LAAC,4MAAA,CAAA,cAAW;;gCAAC;8CAEX,6LAAC,gKAAA,CAAA,UAAU;oCACT,MAAK;oCACL,OAAM;oCACN,SAAS,IAAM,oBAAoB;oCACnC,cAAW;oCACX,IAAI;wCAAE,UAAU;wCAAY,OAAO;wCAAI,KAAK;oCAAG;8CAE/C,cAAA,6LAAC,8JAAA,CAAA,UAAU;;;;;;;;;;;;;;;;sCAGf,6LAAC,kNAAA,CAAA,gBAAa;;8CACZ,6LAAC,sMAAA,CAAA,YAAS;oCACR,SAAS;oCACT,SAAQ;oCACR,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,IAAI;wCAAE,IAAI;wCAAG,UAAU;wCAAI,IAAI;oCAAE;oCACjC,YAAY;wCACV,cACE,6BACE,6LAAC,gKAAA,CAAA,UAAU;4CACT,cAAW;4CACX,MAAK;4CACL,SAAS,IAAM,eAAe;4CAC9B,MAAK;4CACL,IAAI;gDAAE,IAAI;4CAAI;sDAEd,cAAA,6LAAC,8JAAA,CAAA,UAAU;gDAAC,UAAS;;;;;;;;;;;oCAI7B;;;;;;gCAED,eAAe,MAAM,GAAG,kBACvB,6LAAC,uLAAA,CAAA,OAAI;8CACF,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC,mMAAA,CAAA,WAAQ;4CAEP,MAAM;4CACN,SAAS;gDACP,gBAAgB;gDAChB,oBAAoB;4CACtB;4CACA,IAAI;gDACF,SAAS;gDACT,gBAAgB;gDAChB,cAAc;gDACd,SAAS;4CACX;;8DAEA,6LAAC,yMAAA,CAAA,aAAU;8DAAE,KAAK,IAAI;;;;;;8DACtB,6LAAC,yMAAA,CAAA,aAAU;oDACT,IAAI;wDACF,YAAY;wDACZ,OAAO;wDACP,IAAI;oDACN;;wDACD;wDACG,KAAK,KAAK;;;;;;;;2CArBT;;;;;;;;;yDA2BX,6LAAC,yMAAA,CAAA,aAAU;oCACT,IAAI;wCAAE,WAAW;wCAAU,IAAI;wCAAG,OAAO;oCAAO;8CACjD;;;;;;;;;;;;;;;;;;8BAOP,6LAAC,6LAAA,CAAA,SAAM;oBACL,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,UAAS;;sCAET,6LAAC,4MAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,kNAAA,CAAA,gBAAa;;8CACZ,6LAAC,+NAAA,CAAA,eAAY;oCACX,QAAQ;oCACR,IAAG;oCACH,SAAS;oCACT,gBAAgB,CAAC,SAAW,OAAO,WAAW,IAAI,OAAO,IAAI,IAAI,UAAU;oCAC3E,SAAS;oCACT,OAAO;oCACP,YAAY;oCACZ,eAAe;oCACf,UAAU;oCACV,aAAa,CAAC,uBACZ,6LAAC,sMAAA,CAAA,YAAS;4CACP,GAAG,MAAM;4CACV,OAAM;4CACN,SAAQ;4CACR,IAAI;gDAAE,IAAI;gDAAG,OAAO;4CAAO;4CAC3B,QAAQ;4CACR,YAAY;gDACV,GAAG,OAAO,UAAU;gDACpB,4BACE;;wDACG,+BAAiB,6LAAC,2NAAA,CAAA,mBAAgB;4DAAC,OAAM;4DAAU,MAAM;;;;;qEAAS;wDAClE,OAAO,UAAU,CAAC,YAAY;;;4CAGrC;;;;;;;;;;;8CAIN,6LAAC,sMAAA,CAAA,YAAS;oCACR,IAAG;oCACH,SAAQ;oCACR,OAAM;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,IAAI;wCAAE,IAAI;wCAAG,OAAO;oCAAO;oCAC3B,QAAQ;;;;;;;;;;;;sCAGZ,6LAAC,kNAAA,CAAA,gBAAa;;8CACZ,6LAAC,6LAAA,CAAA,SAAM;oCAAC,SAAS;8CAA6B;;;;;;8CAC9C,6LAAC,6LAAA,CAAA,SAAM;oCACL,SAAS;oCACT,OAAM;oCACN,SAAQ;8CACT;;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAO,KAAK;oBAAW,OAAO;wBAAE,SAAS;oBAAO;;;;;;;gBAGhD,iCAAmB,6LAAC;8DAAc;8BAAoB;;;;;;gBACtD,+BAAiB,6LAAC;8DAAc;8BAAa;;;;;;;;;;;;;;;;;;;;;AA0BtD;GA/+CM;KAAA;uCAi/CS"}}, {"offset": {"line": 2407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/components/overlay/welcome-overlay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { IconButton } from \"@mui/material\";\r\nimport ArrowUpwardIcon from \"@mui/icons-material/ArrowUpward\";\r\n\r\ninterface WelcomeOverlayProps {\r\n  onProceedToCart: () => void;\r\n}\r\n\r\nconst WelcomeOverlay: React.FC<WelcomeOverlayProps> = ({ onProceedToCart }) => {\r\n  const [isAnimating, setIsAnimating] = useState(false);\r\n\r\n  const handleArrowClick = () => {\r\n    setIsAnimating(true);\r\n    // Wait for animation to complete before calling onProceedToCart\r\n    setTimeout(() => {\r\n      onProceedToCart();\r\n    }, 800); // Animation duration\r\n  };\r\n\r\n  return (\r\n    <div \r\n      className={`h-screen w-full relative overflow-hidden transition-transform duration-700 ease-in-out ${\r\n        isAnimating ? 'transform -translate-y-full' : 'transform translate-y-0'\r\n      }`}\r\n      style={{\r\n        backgroundImage: `url('/synecxai.png')`,\r\n        backgroundSize: 'cover',\r\n        backgroundPosition: 'center',\r\n        backgroundRepeat: 'no-repeat'\r\n      }}\r\n    >\r\n      {/* <PERSON> at Bottom */}\r\n      <div className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2 z-20\">\r\n        <IconButton\r\n          onClick={handleArrowClick}\r\n          sx={{\r\n            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n            color: \"#3B82F6\",\r\n            padding: \"1.5rem\",\r\n            \"&:hover\": {\r\n              backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n              transform: \"scale(1.1)\",\r\n            },\r\n            transition: \"all 0.3s ease-in-out\",\r\n            boxShadow: \"0 8px 32px rgba(0, 0, 0, 0.2)\",\r\n            border: \"2px solid rgba(59, 130, 246, 0.3)\",\r\n          }}\r\n        >\r\n          <ArrowUpwardIcon sx={{ fontSize: \"2.5rem\" }} />\r\n        </IconButton>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WelcomeOverlay;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUA,MAAM,iBAAgD,CAAC,EAAE,eAAe,EAAE;;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB;QACvB,eAAe;QACf,gEAAgE;QAChE,WAAW;YACT;QACF,GAAG,MAAM,qBAAqB;IAChC;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uFAAuF,EACjG,cAAc,gCAAgC,2BAC9C;QACF,OAAO;YACL,iBAAiB,CAAC,oBAAoB,CAAC;YACvC,gBAAgB;YAChB,oBAAoB;YACpB,kBAAkB;QACpB;kBAGA,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,yMAAA,CAAA,aAAU;gBACT,SAAS;gBACT,IAAI;oBACF,iBAAiB;oBACjB,OAAO;oBACP,SAAS;oBACT,WAAW;wBACT,iBAAiB;wBACjB,WAAW;oBACb;oBACA,YAAY;oBACZ,WAAW;oBACX,QAAQ;gBACV;0BAEA,cAAA,6LAAC,mKAAA,CAAA,UAAe;oBAAC,IAAI;wBAAE,UAAU;oBAAS;;;;;;;;;;;;;;;;;;;;;AAKpD;GA7CM;KAAA;uCA+CS"}}, {"offset": {"line": 2493, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2499, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Synecx%20AI%20Labs/SELF-CHECKOUT/SELF-CHECKOUT/src/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport Cart from \"@/components/cart/cart\";\r\nimport WelcomeOverlay from \"@/components/overlay/welcome-overlay\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\nexport default function Home() {\r\n  const [totalPrice, setTotalPrice] = useState(0);\r\n  const [showCart, setShowCart] = useState(false);\r\n\r\n  const handleUpdateTotalPrice = (price: number) => {\r\n    setTotalPrice(price);\r\n  };\r\n\r\n  const handleProceedToCart = () => {\r\n    setShowCart(true);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {!showCart ? (\r\n        <WelcomeOverlay onProceedToCart={handleProceedToCart} />\r\n      ) : (\r\n        <Cart onUpdateTotalPrice={handleUpdateTotalPrice} />\r\n      )}\r\n      <ToastContainer\r\n        position=\"top-right\"\r\n        autoClose={3000}\r\n        hideProgressBar={false}\r\n        newestOnTop={false}\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme=\"light\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,MAAM,sBAAsB;QAC1B,YAAY;IACd;IAEA,qBACE,6LAAC;;YACE,CAAC,yBACA,6LAAC,sJAAA,CAAA,UAAc;gBAAC,iBAAiB;;;;;qCAEjC,6LAAC,qIAAA,CAAA,UAAI;gBAAC,oBAAoB;;;;;;0BAE5B,6LAAC,sJAAA,CAAA,iBAAc;gBACb,UAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,aAAa;gBACb,YAAY;gBACZ,KAAK;gBACL,gBAAgB;gBAChB,SAAS;gBACT,YAAY;gBACZ,OAAM;;;;;;;;;;;;AAId;GAjCwB;KAAA"}}, {"offset": {"line": 2570, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}