"use client";
import React, { useEffect, useState } from "react";
import Final from "../checkout-final/checkout-final";

const SleepScreenAd: React.FC = () => {
  const gifs = [
    "/synecxai.png",
  "/add.png",

    // "/cake.jpg",
    // "/chocogif.gif",
    // "/sweets.png",
  ];

  const [currentGifIndex, setCurrentGifIndex] = useState(0);
  const [showNewComponent, setShowNewComponent] = useState(false);
  const [slideUp, setSlideUp] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentGifIndex((prevIndex) => (prevIndex + 1) % gifs.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [gifs.length]);

  const handleClick = () => {
    setSlideUp(true);
    setTimeout(() => {
      setShowNewComponent(true);
    }, 500);
  };

  return (
    <>
      <Final />

      {!showNewComponent && (
        <div
          className={`absolute top-0 left-0 w-full h-screen transition-transform duration-500 ${
            slideUp ? "-translate-y-full" : "translate-y-0"
          }`}
          style={{
            backgroundImage: `url(${gifs[currentGifIndex]})`,
            backgroundSize: "100% 100%",
            backgroundPosition: "center",
            zIndex: 50,
          }}
          onClick={handleClick}
        >
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-lg font-bold bg-black bg-opacity-50 px-4 py-2 rounded">
            Tap to Start
          </div>
        </div>
      )}
    </>
  );
};

export default SleepScreenAd;
