import mongoose from 'mongoose';

const MenuItem1Schema = new mongoose.Schema({
  productName: {
    type: String,
    required: true,
  },
  barcode: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
  },
  shortCode: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  orderType: {
    type: String,
    required: true,
    enum: ['Delivery', 'Dine-In', 'Pickup'],
  },
}, {
  timestamps: true,
});

export default mongoose.models.MenuItem1 || mongoose.model('MenuItem1', MenuItem1Schema);
