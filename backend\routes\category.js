const router = require('express').Router();
const Category = require('../models/Category');
const Product = require('../models/Product');

router.get('/categories', async (req, res) => {
  try {
    const categories = await Category.find();
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching categories', error });
  }
});

router.get('/check', async (req, res) => {
  try {
    const { category_name } = req.query;
    const existingCategory = await Category.findOne({ category_name: category_name });
    if (existingCategory) {
      return res.json({ exists: true });
    } else {
      return res.json({ exists: false });
    }
  } catch (error) {
    console.error('Error checking category name:', error);
    return res.status(500).json({ error: 'Server error' });
  }
});
router.delete('/categories/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await Category.findByIdAndDelete(id);
    if (!result) {
      return res.status(404).json({ message: 'Category not found' });
    }
    res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting category', error });
  }
});
router.post('/categories', async (req, res) => {
  try {
    const { category_name } = req.body;
    const existingCategory = await Category.findOne({ category_name });
    if (existingCategory) {
      return res.status(400).json({ message: 'Category already exists' });
    }

    const newCategory = new Category({ category_name });
    const result = await newCategory.save();
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error inserting category', error });
  }
});

router.put('/cat/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { category_name } = req.body;

    if (!category_name) {
      return res.status(400).json({ message: 'Category name is required' });
    }

    const updatedCategory = await Category.findByIdAndUpdate(
      id,
      { category_name },
      { new: true, runValidators: true }
    );

    if (!updatedCategory) {
      return res.status(404).json({ message: 'Category not found' });
    }

    res.status(200).json(updatedCategory);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error updating category', error });
  }
});

router.get('/category-count', async (req, res) => {
  try {
    const products = await Product.find({});

    const productCategoryCount = {};

    products.forEach((product) => {
      const { category } = product; 
      if (productCategoryCount[category]) {
        productCategoryCount[category] += 1; 
      } else {
        productCategoryCount[category] = 1; 
      }
    });
 
    res.status(200).json({
      message: 'Product category count fetched successfully',
      productCategoryCount, 
    });
  } catch (err) {
    console.error('Error fetching product category count:', err);
    res.status(400).json({ message: 'Error fetching product category count', error: err.message });
  }
});


module.exports = router;
