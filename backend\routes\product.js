const express = require("express");
const router = express.Router();
const path = require("path");
const multer = require("multer");
const Product = require("../models/Product");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/");
  },
  filename: (req, file, cb) => {
    const productName = req.body.productName;
    const sanitizedProductName = productName.replace(/\s+/g, "_").toLowerCase();
    const fileExtension = path.extname(file.originalname);
    cb(null, `${sanitizedProductName}${fileExtension}`);
  },
});

const upload = multer({ storage: storage });

router.post("/", upload.single("productImage"), async (req, res) => {
  try {
    const {
      productName,
      price,
      pricePerKg,
      quantity,
      productCode,
      description,
      category,
      supplier,
      selectedOption,
      barcode,
    } = req.body;
    const productImage = req.file ? req.file.filename : null;

    const newProduct = new Product({
      productName,
      barcode,
      price,
      pricePerKg,
      quantity,
      weight: req.body.weight || null,
      productCode,
      description,
      category,
      supplier,
      productImage,
      selectedOption,
    });

    await newProduct.save();
    res.status(201).json({ message: "Product added successfully", newProduct });
  } catch (err) {
    console.error("Error adding product:", err);
    res
      .status(400)
      .json({ message: "Error adding product", error: err.message });
  }
});

router.patch("/reduce", async (req, res) => {
  try {
    const { productName, quantity, weight } = req.body;

    if (!productName) {
      return res.status(400).json({ message: "Product name is required" });
    }

    const product = await Product.findOne({ productName });
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    if (quantity !== undefined) {
      product.quantity = Math.max(0, (product.quantity || 0) - quantity);
    }
    if (weight !== undefined) {
      product.weight = Math.max(0, (product.weight || 0) - weight);
    }

    await product.save();
    res
      .status(200)
      .json({ message: "Product inventory updated successfully", product });
  } catch (err) {
    console.error("Error updating product inventory:", err);
    res
      .status(500)
      .json({ message: "Error updating inventory", error: err.message });
  }
});

router.get("/:id", async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }
    res.json(product);
  } catch (err) {
    console.error("Error fetching product:", err);
    res
      .status(500)
      .json({ message: "Error fetching product", error: err.message });
  }
});

router.put("/:id", async (req, res) => {
  try {
    const updatedProduct = await Product.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    if (!updatedProduct) {
      return res.status(404).json({ message: "Product not found" });
    }
    res.json({ message: "Product updated successfully", updatedProduct });
  } catch (err) {
    console.error("Error updating product:", err);
    res
      .status(500)
      .json({ message: "Error updating product", error: err.message });
  }
});

router.delete("/category/:category_name", async (req, res) => {
  const { category_name } = req.params;

  try {
    const deletedProducts = await Product.deleteMany({ category_name });

    if (deletedProducts.deletedCount === 0) {
      return res
        .status(404)
        .json({ message: "No products found under this category" });
    }

    const deletedCategory = await Category.findOneAndDelete({ category_name });

    if (!deletedCategory) {
      return res.status(404).json({ message: "Category not found" });
    }

    return res.status(200).json({
      message: `Category "${category_name}" and its associated products have been deleted successfully.`,
    });
  } catch (error) {
    console.error(error);
    return res
      .status(500)
      .json({ message: "Error deleting category and products", error });
  }
});

// router.get("/search", async (req, res) => {
//   try {
//     const query = req.query.query;
//     const products = await Product.find({
//       $or: [
//         { shortCode: query },
//         { barcode: query },
//         { productName: { $regex: query, $options: "i" } },
//       ],
//     });
//     res.json(products);
//   } catch (error) {
//     res.status(500).json({ error: "Error searching for products" });
//   }
// });

router.get("/", async (req, res) => {
  try {
    const products = await Product.find().sort({ productName: 1 });
    res.status(200).json(products);
  } catch (err) {
    res
      .status(500)
      .json({ message: "Error fetching products", error: err.message });
  }
});

router.get("/check", async (req, res) => {
  const { productName } = req.query;

  if (!productName) {
    return res.status(400).json({ error: "Product name is required" });
  }

  try {
    const productExists = await Product.findOne({ name: productName });

    if (productExists) {
      return res.json({ exists: true });
    } else {
      return res.json({ exists: false });
    }
  } catch (error) {
    console.error("Error checking product:", error);
    res.status(500).json({ error: "Error checking product" });
  }
});

router.put("/replacement", async (req, res) => {
  const { productName, quantity } = req.body;
  console.log("Received productName:", productName);
  console.log("Received quantity:", quantity);

  try {
    const product = await Product.findOne({
      productName: new RegExp(`^${productName}$`, "i"),
    });

    if (product) {
      product.quantity += quantity;
      await product.save();
      res
        .status(200)
        .send({ message: "Product quantity updated successfully" });
    } else {
      res.status(404).send({ message: "Product not found" });
    }
  } catch (error) {
    res.status(500).send({ message: "Error updating product quantity", error });
  }
});

router.post("/update-quantity", async (req, res) => {
  const { cartItems } = req.body;

  try {
    for (let item of cartItems) {
      const { productName, quantity } = item;
      const product = await Product.findOne({
        productName: new RegExp(`^${productName}$`, "i"),
      });

      if (product) {
        product.quantity -= quantity;
        if (product.quantity < 0) {
          product.quantity = 0;
        }
        await product.save();
      } else {
        return res
          .status(404)
          .json({ message: `Product with name ${productName} not found` });
      }
    }
    res
      .status(200)
      .json({ message: "Product quantities updated successfully" });
  } catch (error) {
    console.error("Error updating product quantities:", error);
    res.status(500).json({
      message: "Error updating product quantities",
      error: error.message,
    });
  }
});

router.put("/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const updatedProduct = await Product.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    if (!updatedProduct) {
      return res.status(404).json({ message: "Product not found" });
    }
    res
      .status(200)
      .json({ message: "Product updated successfully", updatedProduct });
  } catch (err) {
    res
      .status(400)
      .json({ message: "Error updating product", error: err.message });
  }
});

router.delete("/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const deletedProduct = await Product.findByIdAndDelete(id);
    if (!deletedProduct) {
      return res.status(404).json({ message: "Product not found" });
    }
    res
      .status(200)
      .json({ message: "Product deleted successfully", deletedProduct });
  } catch (err) {
    res
      .status(400)
      .json({ message: "Error deleting product", error: err.message });
  }
});

router.get("/barcode/:barcode", async (req, res) => {
  try {
    console.log("Looking up barcode:", req.params.barcode);
    const product = await Product.findOne({ barcode: req.params.barcode });
    console.log("Found product:", product);

    if (!product) {
      console.log("No product found for barcode:", req.params.barcode);
      return res.status(404).json({ message: "Product not found" });
    }

    res.json(product);
  } catch (error) {
    console.error("Error looking up barcode:", error);
    res.status(500).json({ message: "Server error" });
  }
});

module.exports = router;
