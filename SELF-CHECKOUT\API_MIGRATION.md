# API Migration Guide

This document explains the migration from the separate Express backend to Next.js API routes.

## Changes Made

### 1. Backend Consolidation
- Migrated Express server endpoints to Next.js API routes
- Moved MongoDB models to `src/models/`
- Created database connection utility in `src/lib/mongodb.js`
- All API endpoints now available at `http://localhost:3000/api/`

### 2. API Endpoints Available

#### Products
- `GET /api/products` - Get all products with pagination
- `POST /api/products` - Create new product
- `GET /api/search?query=<term>` - Search products
- `GET /api/pricePerKg?productName=<name>` - Get price per kg
- `GET /api/pricePer?productName=<name>` - Get price per item

#### Categories
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create new category

#### Users & Authentication
- `GET /api/users` - Get all users
- `POST /api/users` - Create new user
- `POST /api/auth/login` - User login

#### File Upload
- `POST /api/upload` - Upload files (images/CSV)
- `POST /api/import-csv` - Import products from CSV

### 3. Environment Variables
Create `.env.local` file with:
```
MONGODB_URI=mongodb://localhost:27017/selfcheckout
JWT_SECRET=your-super-secret-jwt-key-here-make-it-very-long-and-random
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. Dependencies Added
- mongoose
- bcrypt
- jsonwebtoken
- dotenv
- csv-parser
- uuid

## Benefits of This Migration

1. **Single Deployment**: Only one server to deploy and maintain
2. **No CORS Issues**: Frontend and API on same origin
3. **Better Performance**: Reduced network latency
4. **Simplified Architecture**: Leverages Next.js full-stack capabilities
5. **Unified Codebase**: Everything in one repository

## How to Run

1. Start the Next.js development server:
   ```bash
   npm run dev
   ```

2. The API will be available at `http://localhost:3000/api/`
3. The frontend will be available at `http://localhost:3000`

## Testing the APIs

You can test the APIs using curl or Postman:

```bash
# Test search API
curl "http://localhost:3000/api/search?query=milk"

# Test price API
curl "http://localhost:3000/api/pricePer?productName=Sample Product"

# Test categories API
curl "http://localhost:3000/api/categories"
```

## File Uploads
- Images are uploaded to `public/uploads/`
- CSV files are uploaded to `public/uploads/csv/`
- Files are accessible via `/uploads/filename` URLs

## Migration Complete
The cart component has been updated to use the new API endpoints:
- Changed `http://localhost:5000/` to `/api/`
- All functionality preserved
- Performance improved due to same-origin requests
