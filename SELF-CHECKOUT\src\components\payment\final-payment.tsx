// "use client";
// import React, { useState, useEffect } from "react";
// import Qr from "./qr";
// import PaymentAnimation from "./payment-animation";

// interface FinalPaymentProps {
//   totalPrice: number;
// }

// const FinalPayment: React.FC<FinalPaymentProps> = ({ totalPrice }) => {
//   const [showQr, setShowQr] = useState(true);

//   useEffect(() => {
//     const timer = setTimeout(() => {
//       setShowQr(false);
//     }, 8000);

//     return () => clearTimeout(timer);
//   }, []);

//   return (
//     <div className="flex justify-center items-center h-full w-auto ">
//       {showQr ? <Qr totalPrice={totalPrice} /> : <PaymentAnimation />}
//     </div>
//   );
// };

// export default FinalPayment;

"use client";
import React, { useState, useEffect } from "react";
import Qr from "./qr";
import PaymentAnimation from "./payment-animation";
import CheckoutAd from "../ads/checkout-ad"; // Import CheckoutAd directly

interface FinalPaymentProps {
  totalPrice: number;
}

const FinalPayment: React.FC<FinalPaymentProps> = ({ totalPrice }) => {
  const [showQr, setShowQr] = useState(true);
  const [showPaymentAnimation, setShowPaymentAnimation] = useState(false);
  const [showCheckoutAd, setShowCheckoutAd] = useState(false);

  useEffect(() => {
    // First transition: Show QR for 8 seconds, then show Payment Animation
    const qrTimer = setTimeout(() => {
      setShowQr(false);
      setShowPaymentAnimation(true);
    }, 8000);

    // Second transition: Show Payment Animation for 3 seconds, then show CheckoutAd
    const paymentTimer = setTimeout(() => {
      setShowPaymentAnimation(false);
      setShowCheckoutAd(true);
      
      // Clear cart items from localStorage
      localStorage.removeItem("cartItems");

      // Reload the page after data is cleared
      setTimeout(() => {
        window.location.reload();
      }, 500); // Small delay to ensure smooth transition
    }, 11000); // 11 seconds total (8s QR + 3s animation)

    return () => {
      clearTimeout(qrTimer);
      clearTimeout(paymentTimer);
    };
  }, []);

  return (
    <div className="flex justify-center items-center h-full w-auto">
      {showQr && <Qr totalPrice={totalPrice} />}
      {showPaymentAnimation && <PaymentAnimation />}
      {showCheckoutAd && <CheckoutAd totalPrice={totalPrice} />}
    </div>
  );
};

export default FinalPayment;
