{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.1", "@mui/material": "^5.16.4", "axios": "^1.7.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "bwip-js": "^4.5.0", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "moment": "^2.30.1", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "multer": "^1.4.5-lts.1", "pdfkit": "^0.15.0", "uuid": "^10.0.0"}}