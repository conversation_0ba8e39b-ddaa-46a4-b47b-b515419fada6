import type { <PERSON>ada<PERSON> } from "next";

import "./globals.css";

import { <PERSON><PERSON><PERSON> } from "next/font/google"; // Importing Roboto instead of Quicksand

const roboto = Nunito({ subsets: ["latin"], weight: ["400", "700"] }); // Adjust weights as needed

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={roboto.className}>
        {children}
      </body>
    </html>
  );
}
