import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Product from '@/models/Product';

// GET /api/search?query=miranda
export async function GET(request) {
  try {
    await dbConnect();
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    if (!query) {
      return NextResponse.json([], { status: 200 });
    }
    // Case-insensitive, partial match on productName
    const products = await Product.find({
      productName: { $regex: query, $options: 'i' }
    }).limit(20);
    return NextResponse.json(products);
  } catch (error) {
    console.error('Error in /api/search:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
